<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>


<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $pro['name'] ?></h1>
                <h5><?= $pro['procode'] ?></h5>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>open_projects/<?= $pro['ucode'] ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> View Project</a></li>
                    <li class="breadcrumb-item active"><?= $pro['name'] ?></li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">
    <div class="row p-2">
        <div class=" col-md-12">

            <div class="card">
                <div class="card-header bg-info">
                    <a href="<?= base_url() ?>open_projects/<?= $pro['ucode'] ?>" class="btn btn-dark btn-sm"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Back </a>
                  <div class="float-right">  Edit Project Status</div>
                    
                </div>
                <?= form_open('update_projects_status') ?>
                <div class="card-body">

                    <div class="form-group">
                        <div class="form-group">
                            <label class=" text-dark">Project Status</label>
                            <select class="form-control" name="status" id="">
                                <option selected value="<?= $pro['status'] ?>"><?= ucfirst($pro['status']) ?></option>
                                <option value="active">Active</option>
                                <option value="hold">Hold</option>
                                <option value="completed">Completed</option>
                                <option value="canceled">Canceled</option>
                            </select>
                        </div>

                    </div>

                    <div class="form-group">
                        <div class="form-group">
                            <label for="my-textarea">Notes</label>
                            <textarea id="my-textarea" class="form-control" name="statusnotes" rows="3" required><?= $pro['statusnotes'] ?></textarea>
                        </div>
                    </div>

                </div>
                <div class="card-footer">
                    <span><b>Status Set: </b> <?= datetimeforms($pro['status_at']) ?> / <?= ($pro['status_by']) ?> </span>
                    <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                    <input type="hidden" name="proid" value="<?= $pro['id'] ?>">
                    <button type="submit" class="btn btn-info float-right">
                        <i class="fa fa-save" aria-hidden="true"></i> Save Changes
                    </button>
                </div>
                <?= form_close() ?>
            </div>

        </div>
    </div>
</section>


</body>

<script>
    $(document).ready(function() {
        $('#province').change(function() {
            var province_code = $(this).val();

            $.ajax({
                url: '<?= base_url() ?>getaddress',
                type: 'post',
                data: {
                    province_code: province_code
                },
                dataType: 'json',
                success: function(response) {
                    console.log(response);
                    var len = response.district.length;

                    $("#district").empty();
                    $("#district").append("<option value=''>Select a District</option>");

                    for (var i = 0; i < len; i++) {
                        var code = response.district[i]['districtcode'];
                        var name = response.district[i]['name'];
                        //var code = response.subcategories[i]['code'];

                        $("#district").append("<option value='" + code + "'>" + name +
                            "</option>");

                    }
                }
            });
        });



        $('#district').change(function() {
            var district_code = $(this).val();

            $.ajax({
                url: '<?= base_url() ?>getaddress',
                type: 'post',
                data: {
                    district_code: district_code
                },
                dataType: 'json',
                success: function(response) {
                    console.log(response);
                    var len = response.llgs.length;
                    $("#llg").empty();
                    $("#llg").append("<option value=''>Select a LLG</option>");
                    for (var i = 0; i < len; i++) {

                        var code = response.llgs[i]['llgcode'];
                        var name = response.llgs[i]['name'];

                        $("#llg").append("<option value='" + code + "'>" + name + "</option>");
                    }
                }
            });
        });


    });
</script>



<?= $this->endSection() ?>