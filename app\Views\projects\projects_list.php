<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>



<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">

            <div class="col-sm-6">
                <h1 class="m-0">Projects List</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard"><i class="fa fa-tachometer-alt" aria-hidden="true"></i> Dashboard</a></li>
                    <li class="breadcrumb-item active">Project List</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info">
                    <a href="<?= base_url() ?>dashboard" class="btn btn-dark btn-sm"> <i class="fa fa-arrow-left" aria-hidden="true"></i> Back</a>
                    <span class=" float-right"><i class="fa fa-list" aria-hidden="true"></i> Projects List</span>
                </div>
                <div class="card-body table-responsive ">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>P.Code</th>
                                <th>Project</th>
                                <th>Status</th>
                                <th>Budget</th>
                                <th>Payments</th>
                                <th>Contractor</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $x = 1;
                            foreach ($projects as $key) : ?>
                                <tr>
                                    <td scope="row"><?= $x++ ?></td>
                                    <td><?= $key['procode'] ?></td>
                                    <td><?= $key['name'] ?></td>
                                    <td><?= get_status_icon($key['status']) ?></td>
                                    <?php
                                    $payment = array();
                                    foreach ($fund as $fd) : ?>
                                        <?php
                                        if ($key['procode'] == $fd['procode']) {
                                            $payment[] = $fd['amount'];
                                        }
                                        ?>
                                    <?php endforeach; ?>
                                    <td><?= COUNTRY_CURRENCY ?><?= number_format($key['budget'], 2) ?></td>
                                    <td><?= COUNTRY_CURRENCY ?><?= number_format(array_sum($payment), 2) ?></td>
                                    <td><?= $key['contractor_name'] ?></td>
                                    <td><a href="<?= base_url() ?>open_projects/<?= $key['ucode'] ?>" class="btn btn-info btn-sm "> View <i class="fa fa-angle-right" aria-hidden="true"></i> </a></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>




</body>


<?= $this->endSection() ?>