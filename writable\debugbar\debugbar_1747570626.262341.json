{"url": "http://localhost/promis/index.php/", "method": "GET", "isAJAX": false, "startTime": **********.951576, "totalTime": 154.**************, "totalMemory": "7.431", "segmentDuration": 25, "segmentCount": 7, "CI_VERSION": "4.3.2", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.984819, "duration": 0.*****************}, {"name": "Routing", "component": "Timer", "start": **********.022321, "duration": 6.508827209472656e-05}, {"name": "Before Filters", "component": "Timer", "start": **********.025175, "duration": 2.09808349609375e-05}, {"name": "Controller", "component": "Timer", "start": **********.025198, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.025199, "duration": 0.*****************}, {"name": "After Filters", "component": "Timer", "start": **********.105899, "duration": 0.0009598731994628906}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(3 total Queries, 3 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.9 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `projects`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:241", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:618", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Home.php:53", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Home->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Home.php:53", "qid": "834cc3b804dd8202f00e9882678835d6"}, {"hover": "", "class": "", "duration": "0.56 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `project_milestones`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:203", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:557", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Home.php:54", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Home->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Home.php:54", "qid": "4a6e7ba577ec9b666d71b274e072b488"}, {"hover": "", "class": "", "duration": "0.7 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `profund`\n<strong>ORDER</strong> <strong>BY</strong> `paymentdate` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:203", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:557", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Home.php:55", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Home->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Home.php:55", "qid": "5941d568ec8c85986971d6a43defea51"}]}, "badgeValue": 3, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.090107, "duration": "0.003586"}, {"name": "Query", "component": "Database", "start": **********.095512, "duration": "0.000896", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `projects`"}, {"name": "Query", "component": "Database", "start": **********.100117, "duration": "0.000561", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `project_milestones`"}, {"name": "Query", "component": "Database", "start": **********.100898, "duration": "0.000704", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `profund`\n<strong>ORDER</strong> <strong>BY</strong> `paymentdate` <strong>ASC</strong>"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "info", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: templates/nolstemp.php", "component": "Views", "start": **********.104165, "duration": 0.0010960102081298828}, {"name": "View: home/home.php", "component": "Views", "start": **********.102285, "duration": 0.003342151641845703}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 170 )", "display": {"coreFiles": [{"name": "AutoRouterImproved.php", "path": "SYSTEMPATH\\Router\\AutoRouterImproved.php"}, {"name": "AutoRouterInterface.php", "path": "SYSTEMPATH\\Router\\AutoRouterInterface.php"}, {"name": "AutoloadConfig.php", "path": "SYSTEMPATH\\Config\\AutoloadConfig.php"}, {"name": "Autoloader.php", "path": "SYSTEMPATH\\Autoloader\\Autoloader.php"}, {"name": "BaseBuilder.php", "path": "SYSTEMPATH\\Database\\BaseBuilder.php"}, {"name": "BaseCollector.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php"}, {"name": "BaseConfig.php", "path": "SYSTEMPATH\\Config\\BaseConfig.php"}, {"name": "BaseConnection.php", "path": "SYSTEMPATH\\Database\\BaseConnection.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php"}, {"name": "BaseModel.php", "path": "SYSTEMPATH\\BaseModel.php"}, {"name": "BaseResult.php", "path": "SYSTEMPATH\\Database\\BaseResult.php"}, {"name": "BaseService.php", "path": "SYSTEMPATH\\Config\\BaseService.php"}, {"name": "Builder.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php"}, {"name": "CacheFactory.php", "path": "SYSTEMPATH\\Cache\\CacheFactory.php"}, {"name": "CacheInterface.php", "path": "SYSTEMPATH\\Cache\\CacheInterface.php"}, {"name": "CloneableCookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php"}, {"name": "CodeIgniter.php", "path": "SYSTEMPATH\\CodeIgniter.php"}, {"name": "Common.php", "path": "SYSTEMPATH\\Common.php"}, {"name": "ConditionalTrait.php", "path": "SYSTEMPATH\\Traits\\ConditionalTrait.php"}, {"name": "Config.php", "path": "SYSTEMPATH\\Database\\Config.php"}, {"name": "Connection.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php"}, {"name": "ConnectionInterface.php", "path": "SYSTEMPATH\\Database\\ConnectionInterface.php"}, {"name": "ContentSecurityPolicy.php", "path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php"}, {"name": "Controller.php", "path": "SYSTEMPATH\\Controller.php"}, {"name": "Cookie.php", "path": "SYSTEMPATH\\Cookie\\Cookie.php"}, {"name": "CookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CookieInterface.php"}, {"name": "CookieStore.php", "path": "SYSTEMPATH\\Cookie\\CookieStore.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Database\\Database.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php"}, {"name": "DebugToolbar.php", "path": "SYSTEMPATH\\Filters\\DebugToolbar.php"}, {"name": "DotEnv.php", "path": "SYSTEMPATH\\Config\\DotEnv.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Events\\Events.php"}, {"name": "Exceptions.php", "path": "SYSTEMPATH\\Debug\\Exceptions.php"}, {"name": "Factories.php", "path": "SYSTEMPATH\\Config\\Factories.php"}, {"name": "Factory.php", "path": "SYSTEMPATH\\Config\\Factory.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php"}, {"name": "FileLocator.php", "path": "SYSTEMPATH\\Autoloader\\FileLocator.php"}, {"name": "Files.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php"}, {"name": "FilterInterface.php", "path": "SYSTEMPATH\\Filters\\FilterInterface.php"}, {"name": "Filters.php", "path": "SYSTEMPATH\\Filters\\Filters.php"}, {"name": "FormatRules.php", "path": "SYSTEMPATH\\Validation\\FormatRules.php"}, {"name": "HandlerInterface.php", "path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php"}, {"name": "Header.php", "path": "SYSTEMPATH\\HTTP\\Header.php"}, {"name": "IncomingRequest.php", "path": "SYSTEMPATH\\HTTP\\IncomingRequest.php"}, {"name": "Logger.php", "path": "SYSTEMPATH\\Log\\Logger.php"}, {"name": "Logs.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php"}, {"name": "Message.php", "path": "SYSTEMPATH\\HTTP\\Message.php"}, {"name": "MessageInterface.php", "path": "SYSTEMPATH\\HTTP\\MessageInterface.php"}, {"name": "MessageTrait.php", "path": "SYSTEMPATH\\HTTP\\MessageTrait.php"}, {"name": "Model.php", "path": "SYSTEMPATH\\Model.php"}, {"name": "Modules.php", "path": "SYSTEMPATH\\Modules\\Modules.php"}, {"name": "OutgoingRequest.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php"}, {"name": "OutgoingRequestInterface.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php"}, {"name": "Query.php", "path": "SYSTEMPATH\\Database\\Query.php"}, {"name": "QueryInterface.php", "path": "SYSTEMPATH\\Database\\QueryInterface.php"}, {"name": "RendererInterface.php", "path": "SYSTEMPATH\\View\\RendererInterface.php"}, {"name": "Request.php", "path": "SYSTEMPATH\\HTTP\\Request.php"}, {"name": "RequestInterface.php", "path": "SYSTEMPATH\\HTTP\\RequestInterface.php"}, {"name": "RequestTrait.php", "path": "SYSTEMPATH\\HTTP\\RequestTrait.php"}, {"name": "Response.php", "path": "SYSTEMPATH\\HTTP\\Response.php"}, {"name": "ResponseInterface.php", "path": "SYSTEMPATH\\HTTP\\ResponseInterface.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\API\\ResponseTrait.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\HTTP\\ResponseTrait.php"}, {"name": "Result.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Result.php"}, {"name": "ResultInterface.php", "path": "SYSTEMPATH\\Database\\ResultInterface.php"}, {"name": "RouteCollection.php", "path": "SYSTEMPATH\\Router\\RouteCollection.php"}, {"name": "RouteCollectionInterface.php", "path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php"}, {"name": "Router.php", "path": "SYSTEMPATH\\Router\\Router.php"}, {"name": "RouterInterface.php", "path": "SYSTEMPATH\\Router\\RouterInterface.php"}, {"name": "Routes.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php"}, {"name": "Services.php", "path": "SYSTEMPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "SYSTEMPATH\\Session\\Session.php"}, {"name": "SessionInterface.php", "path": "SYSTEMPATH\\Session\\SessionInterface.php"}, {"name": "Time.php", "path": "SYSTEMPATH\\I18n\\Time.php"}, {"name": "TimeTrait.php", "path": "SYSTEMPATH\\I18n\\TimeTrait.php"}, {"name": "Timer.php", "path": "SYSTEMPATH\\Debug\\Timer.php"}, {"name": "Timers.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php"}, {"name": "Toolbar.php", "path": "SYSTEMPATH\\Debug\\Toolbar.php"}, {"name": "URI.php", "path": "SYSTEMPATH\\HTTP\\URI.php"}, {"name": "UserAgent.php", "path": "SYSTEMPATH\\HTTP\\UserAgent.php"}, {"name": "Validation.php", "path": "SYSTEMPATH\\Validation\\Validation.php"}, {"name": "ValidationInterface.php", "path": "SYSTEMPATH\\Validation\\ValidationInterface.php"}, {"name": "View.php", "path": "SYSTEMPATH\\Config\\View.php"}, {"name": "View.php", "path": "SYSTEMPATH\\View\\View.php"}, {"name": "ViewDecoratorTrait.php", "path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php"}, {"name": "Views.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php"}, {"name": "array_helper.php", "path": "SYSTEMPATH\\Helpers\\array_helper.php"}, {"name": "bootstrap.php", "path": "SYSTEMPATH\\bootstrap.php"}, {"name": "form_helper.php", "path": "SYSTEMPATH\\Helpers\\form_helper.php"}, {"name": "kint_helper.php", "path": "SYSTEMPATH\\Helpers\\kint_helper.php"}, {"name": "url_helper.php", "path": "SYSTEMPATH\\Helpers\\url_helper.php"}], "userFiles": [{"name": "AbstractRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\AbstractRenderer.php"}, {"name": "App.php", "path": "APPPATH\\Config\\App.php"}, {"name": "Autoload.php", "path": "APPPATH\\Config\\Autoload.php"}, {"name": "BaseController.php", "path": "APPPATH\\Controllers\\BaseController.php"}, {"name": "Cache.php", "path": "APPPATH\\Config\\Cache.php"}, {"name": "ClassLoader.php", "path": "FCPATH\\vendor\\composer\\ClassLoader.php"}, {"name": "CliRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\CliRenderer.php"}, {"name": "Common.php", "path": "APPPATH\\Common.php"}, {"name": "Constants.php", "path": "APPPATH\\Config\\Constants.php"}, {"name": "ContentSecurityPolicy.php", "path": "APPPATH\\Config\\ContentSecurityPolicy.php"}, {"name": "Cookie.php", "path": "APPPATH\\Config\\Cookie.php"}, {"name": "Database.php", "path": "APPPATH\\Config\\Database.php"}, {"name": "Escaper.php", "path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\Escaper.php"}, {"name": "Events.php", "path": "APPPATH\\Config\\Events.php"}, {"name": "Exceptions.php", "path": "APPPATH\\Config\\Exceptions.php"}, {"name": "FacadeInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\FacadeInterface.php"}, {"name": "Feature.php", "path": "APPPATH\\Config\\Feature.php"}, {"name": "Filters.php", "path": "APPPATH\\Config\\Filters.php"}, {"name": "Functions.php", "path": "FCPATH\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php"}, {"name": "Home.php", "path": "APPPATH\\Controllers\\Home.php"}, {"name": "InstalledVersions.php", "path": "FCPATH\\vendor\\composer\\InstalledVersions.php"}, {"name": "Kint.php", "path": "APPPATH\\Config\\Kint.php"}, {"name": "Kint.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Kint.php"}, {"name": "LogLevel.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LogLevel.php"}, {"name": "Logger.php", "path": "APPPATH\\Config\\Logger.php"}, {"name": "LoggerAwareTrait.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerAwareTrait.php"}, {"name": "LoggerInterface.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerInterface.php"}, {"name": "Modules.php", "path": "APPPATH\\Config\\Modules.php"}, {"name": "Paths.php", "path": "APPPATH\\Config\\Paths.php"}, {"name": "RendererInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RendererInterface.php"}, {"name": "RichRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RichRenderer.php"}, {"name": "Routes.php", "path": "APPPATH\\Config\\Routes.php"}, {"name": "Services.php", "path": "APPPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "APPPATH\\Config\\Session.php"}, {"name": "TextRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\TextRenderer.php"}, {"name": "Toolbar.php", "path": "APPPATH\\Config\\Toolbar.php"}, {"name": "UserAgents.php", "path": "APPPATH\\Config\\UserAgents.php"}, {"name": "Utils.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Utils.php"}, {"name": "Validation.php", "path": "APPPATH\\Config\\Validation.php"}, {"name": "View.php", "path": "APPPATH\\Config\\View.php"}, {"name": "autoload.php", "path": "FCPATH\\vendor\\autoload.php"}, {"name": "autoload_real.php", "path": "FCPATH\\vendor\\composer\\autoload_real.php"}, {"name": "autoload_static.php", "path": "FCPATH\\vendor\\composer\\autoload_static.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php80\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php81\\bootstrap.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php"}, {"name": "countryModel.php", "path": "APPPATH\\Models\\countryModel.php"}, {"name": "deep_copy.php", "path": "FCPATH\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php"}, {"name": "development.php", "path": "APPPATH\\Config\\Boot\\development.php"}, {"name": "districtModel.php", "path": "APPPATH\\Models\\districtModel.php"}, {"name": "function.php", "path": "FCPATH\\vendor\\symfony\\deprecation-contracts\\function.php"}, {"name": "functions.php", "path": "FCPATH\\vendor\\symfony\\string\\Resources\\functions.php"}, {"name": "home.php", "path": "APPPATH\\Views\\home\\home.php"}, {"name": "index.php", "path": "FCPATH\\index.php"}, {"name": "info_helper.php", "path": "APPPATH\\Helpers\\info_helper.php"}, {"name": "init.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init.php"}, {"name": "init_helpers.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init_helpers.php"}, {"name": "installed.php", "path": "FCPATH\\vendor\\composer\\installed.php"}, {"name": "nolstemp.php", "path": "APPPATH\\Views\\templates\\nolstemp.php"}, {"name": "orgModel.php", "path": "APPPATH\\Models\\orgModel.php"}, {"name": "platform_check.php", "path": "FCPATH\\vendor\\composer\\platform_check.php"}, {"name": "profundModel.php", "path": "APPPATH\\Models\\profundModel.php"}, {"name": "project_officersModel.php", "path": "APPPATH\\Models\\project_officersModel.php"}, {"name": "projectsModel.php", "path": "APPPATH\\Models\\projectsModel.php"}, {"name": "promilestonesModel.php", "path": "APPPATH\\Models\\promilestonesModel.php"}, {"name": "prophasesModel.php", "path": "APPPATH\\Models\\prophasesModel.php"}, {"name": "provinceModel.php", "path": "APPPATH\\Models\\provinceModel.php"}, {"name": "usersModel.php", "path": "APPPATH\\Models\\usersModel.php"}]}, "badgeValue": 170, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Home", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\Home::logout"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Home::about"}, {"method": "GET", "route": "home_project_one_view/(.*)", "handler": "\\App\\Controllers\\Home::home_project_one_view/$1"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\Admindash::index"}, {"method": "GET", "route": "my_account", "handler": "\\App\\Controllers\\Admindash::my_account"}, {"method": "GET", "route": "reports_dashboard", "handler": "\\App\\Controllers\\Admindash::reports_dashboard"}, {"method": "GET", "route": "report_projects_dash", "handler": "\\App\\Controllers\\ProReports::index"}, {"method": "GET", "route": "report_projects_status/(.*)", "handler": "\\App\\Controllers\\ProReports::report_projects_status/$1"}, {"method": "GET", "route": "report_projects_view/(.*)", "handler": "\\App\\Controllers\\ProReports::report_projects_view/$1"}, {"method": "GET", "route": "report_pro_payment_record/(.*)", "handler": "\\App\\Controllers\\ProReports::report_pro_payment_record/$1"}, {"method": "GET", "route": "report_contractors_dash", "handler": "\\App\\Controllers\\ProReports::report_contractors_dash"}, {"method": "GET", "route": "report_contractors_view/(.*)", "handler": "\\App\\Controllers\\ProReports::report_contractors_view/$1"}, {"method": "GET", "route": "report_pro_officers_dash", "handler": "\\App\\Controllers\\ProReports::report_pro_officers_dash"}, {"method": "GET", "route": "report_pro_officers_view/(.*)", "handler": "\\App\\Controllers\\ProReports::report_pro_officers_view/$1"}, {"method": "GET", "route": "po_dash", "handler": "\\App\\Controllers\\POfficers::index"}, {"method": "GET", "route": "po_open_project/(.*)", "handler": "\\App\\Controllers\\POfficers::po_open_project/$1"}, {"method": "GET", "route": "po_details/(.*)", "handler": "\\App\\Controllers\\POfficers::po_details/$1"}, {"method": "GET", "route": "po_details_info_edit/(.*)", "handler": "\\App\\Controllers\\POfficers::po_details_info_edit/$1"}, {"method": "GET", "route": "po_phases/(.*)", "handler": "\\App\\Controllers\\POfficers::po_phases/$1"}, {"method": "GET", "route": "po_milestones/(.*)", "handler": "\\App\\Controllers\\POfficers::po_milestones/$1"}, {"method": "GET", "route": "po_files_open/(.*)", "handler": "\\App\\Controllers\\POfficers::po_files_open/$1"}, {"method": "GET", "route": "po_funding_open/(.*)", "handler": "\\App\\Controllers\\POfficers::po_funding_open/$1"}, {"method": "GET", "route": "po_events_open/(.*)", "handler": "\\App\\Controllers\\POfficers::po_events_open/$1"}, {"method": "GET", "route": "po_reports_open/(.*)", "handler": "\\App\\Controllers\\POfficers::po_reports_open/$1"}, {"method": "GET", "route": "projects", "handler": "\\App\\Controllers\\Projects::index"}, {"method": "GET", "route": "new_projects", "handler": "\\App\\Controllers\\Projects::create_projects"}, {"method": "GET", "route": "open_projects/(.*)", "handler": "\\App\\Controllers\\Projects::open_projects/$1"}, {"method": "GET", "route": "open_prophases/(.*)", "handler": "\\App\\Controllers\\Projects::open_prophases/$1"}, {"method": "GET", "route": "project_phases/(.*)", "handler": "\\App\\Controllers\\Projects::project_phases/$1"}, {"method": "GET", "route": "edit_projects/(.*)", "handler": "\\App\\Controllers\\Projects::edit_projects/$1"}, {"method": "GET", "route": "milestones/(.*)", "handler": "\\App\\Controllers\\Milestones::pro_milestones/$1"}, {"method": "GET", "route": "open_proevents/(.*)", "handler": "\\App\\Controllers\\Projects::open_proevents/$1"}, {"method": "GET", "route": "getaddress", "handler": "\\App\\Controllers\\Projects::getaddress"}, {"method": "GET", "route": "edit_projects_status/(.*)", "handler": "\\App\\Controllers\\Projects::edit_projects_status/$1"}, {"method": "GET", "route": "edit_projects_contractors/(.*)", "handler": "\\App\\Controllers\\Projects::edit_projects_contractors/$1"}, {"method": "GET", "route": "edit_projects_officers/(.*)", "handler": "\\App\\Controllers\\Projects::edit_projects_officers/$1"}, {"method": "GET", "route": "project_officers", "handler": "\\App\\Controllers\\Project_officers::index"}, {"method": "GET", "route": "proleads", "handler": "\\App\\Controllers\\Proleads::index"}, {"method": "GET", "route": "contractors", "handler": "\\App\\Controllers\\Proleads::contractors_list"}, {"method": "GET", "route": "contractors_new", "handler": "\\App\\Controllers\\Proleads::contractors_new"}, {"method": "GET", "route": "edit_contractors/(.*)", "handler": "\\App\\Controllers\\Proleads::edit_contractors/$1"}, {"method": "GET", "route": "open_contractor/(.*)", "handler": "\\App\\Controllers\\Proleads::open_contractor/$1"}, {"method": "GET", "route": "da<PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::index"}, {"method": "GET", "route": "dlogout", "handler": "\\App\\Controllers\\Dakoii::logout"}, {"method": "GET", "route": "ddash", "handler": "\\App\\Controllers\\Dakoii::ddash"}, {"method": "GET", "route": "dopen_org/(.*)", "handler": "\\App\\Controllers\\Dakoii::open_org/$1"}, {"method": "GET", "route": "dlist_org", "handler": "\\App\\Controllers\\Dakoii::list_org"}, {"method": "GET", "route": "testa", "handler": "\\App\\Controllers\\Test::index"}, {"method": "GET", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "GET", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}, {"method": "GET", "route": "testmap", "handler": "\\App\\Controllers\\Test::testmap"}, {"method": "GET", "route": "test_view", "handler": "\\App\\Controllers\\Test::test_view"}, {"method": "POST", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "POST", "route": "login_po", "handler": "\\App\\Controllers\\Home::login_po"}, {"method": "POST", "route": "update_admin_orglogo", "handler": "\\App\\Controllers\\Admindash::update_admin_orglogo"}, {"method": "POST", "route": "update_admin_orginfo", "handler": "\\App\\Controllers\\Admindash::update_admin_orginfo"}, {"method": "POST", "route": "milestone_notes", "handler": "\\App\\Controllers\\POfficers::milestone_notes"}, {"method": "POST", "route": "milestone_files", "handler": "\\App\\Controllers\\POfficers::milestone_files"}, {"method": "POST", "route": "getdistricts", "handler": "\\App\\Controllers\\POfficers::getdistricts"}, {"method": "POST", "route": "edit_project_budget", "handler": "\\App\\Controllers\\Projects::edit_project_budget"}, {"method": "POST", "route": "set_project_contractor", "handler": "\\App\\Controllers\\Projects::set_project_contractor"}, {"method": "POST", "route": "set_project_officers", "handler": "\\App\\Controllers\\Projects::set_project_officers"}, {"method": "POST", "route": "add_proevents", "handler": "\\App\\Controllers\\Projects::add_proevents"}, {"method": "POST", "route": "edit_proevents", "handler": "\\App\\Controllers\\Projects::edit_proevents"}, {"method": "POST", "route": "edit_projects/(.*)", "handler": "\\App\\Controllers\\Projects::edit_projects/$1"}, {"method": "POST", "route": "new_projects", "handler": "\\App\\Controllers\\Projects::create_projects"}, {"method": "POST", "route": "update_projects_status", "handler": "\\App\\Controllers\\Projects::update_projects_status"}, {"method": "POST", "route": "update_projects_contractors", "handler": "\\App\\Controllers\\Projects::update_projects_contractors"}, {"method": "POST", "route": "update_projects_officers", "handler": "\\App\\Controllers\\Projects::update_projects_officers"}, {"method": "POST", "route": "getaddress", "handler": "\\App\\Controllers\\Projects::getaddress"}, {"method": "POST", "route": "gps_upload", "handler": "\\App\\Controllers\\Projects::gpsfile_upload"}, {"method": "POST", "route": "prodocs_upload", "handler": "\\App\\Controllers\\Projects::prodocs_upload"}, {"method": "POST", "route": "prodocs_edit", "handler": "\\App\\Controllers\\Projects::prodocs_edit"}, {"method": "POST", "route": "prodocs_delete", "handler": "\\App\\Controllers\\Projects::prodocs_delete"}, {"method": "POST", "route": "gps_set", "handler": "\\App\\Controllers\\Projects::gps_set"}, {"method": "POST", "route": "addpayments", "handler": "\\App\\Controllers\\Projects::addpayments"}, {"method": "POST", "route": "editpayments", "handler": "\\App\\Controllers\\Projects::editpayments"}, {"method": "POST", "route": "milestones/(.*)", "handler": "\\App\\Controllers\\Projects::pro_milestones/$1"}, {"method": "POST", "route": "add_phases", "handler": "\\App\\Controllers\\Projects::add_phases"}, {"method": "POST", "route": "edit_phases", "handler": "\\App\\Controllers\\Projects::edit_phases"}, {"method": "POST", "route": "delete_phases", "handler": "\\App\\Controllers\\Projects::delete_phases"}, {"method": "POST", "route": "add_milestones", "handler": "\\App\\Controllers\\Projects::add_milestones"}, {"method": "POST", "route": "edit_milestones", "handler": "\\App\\Controllers\\Projects::edit_milestones"}, {"method": "POST", "route": "delete_milestones", "handler": "\\App\\Controllers\\Projects::delete_milestones"}, {"method": "POST", "route": "pro_status", "handler": "\\App\\Controllers\\Projects::pro_status"}, {"method": "POST", "route": "add_project_officers", "handler": "\\App\\Controllers\\Project_officers::add_project_officers"}, {"method": "POST", "route": "edit_project_officers", "handler": "\\App\\Controllers\\Project_officers::edit_project_officers"}, {"method": "POST", "route": "edit_password_project_officers", "handler": "\\App\\Controllers\\Project_officers::edit_password_project_officers"}, {"method": "POST", "route": "create_contractor", "handler": "\\App\\Controllers\\Proleads::create_contractor"}, {"method": "POST", "route": "update_contractor", "handler": "\\App\\Controllers\\Proleads::update_contractor"}, {"method": "POST", "route": "update_con_contacts", "handler": "\\App\\Controllers\\Proleads::update_con_contacts"}, {"method": "POST", "route": "create_con_files", "handler": "\\App\\Controllers\\Proleads::create_con_files"}, {"method": "POST", "route": "update_con_files", "handler": "\\App\\Controllers\\Proleads::update_con_files"}, {"method": "POST", "route": "delete_con_files", "handler": "\\App\\Controllers\\Proleads::delete_con_files"}, {"method": "POST", "route": "update_con_logo", "handler": "\\App\\Controllers\\Proleads::update_con_logo"}, {"method": "POST", "route": "create_con_notices", "handler": "\\App\\Controllers\\Proleads::create_con_notices"}, {"method": "POST", "route": "dlogin", "handler": "\\App\\Controllers\\Dakoii::login"}, {"method": "POST", "route": "dad<PERSON>g", "handler": "\\App\\Controllers\\Dakoii::addorg"}, {"method": "POST", "route": "deditorg", "handler": "\\App\\Controllers\\Dakoii::editorg"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::adduser"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::create_admin"}, {"method": "POST", "route": "dakoii_update_org_logo", "handler": "\\App\\Controllers\\Dakoii::dakoii_update_org_logo"}, {"method": "POST", "route": "dakoii_update_org_address", "handler": "\\App\\Controllers\\Dakoii::dakoii_update_org_address"}, {"method": "POST", "route": "dakoii_update_org_location_lock", "handler": "\\App\\Controllers\\Dakoii::dakoii_update_org_location_lock"}, {"method": "POST", "route": "dakoii_remove_org_location_lock", "handler": "\\App\\Controllers\\Dakoii::dakoii_remove_org_location_lock"}, {"method": "POST", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "POST", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}]}, "badgeValue": 54, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "5.82", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.13", "count": 3}}}, "badgeValue": 4, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.989644, "duration": 0.005822896957397461}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.09642, "duration": 5.984306335449219e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.100685, "duration": 3.814697265625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.101608, "duration": 3.600120544433594e-05}]}], "vars": {"varData": {"View Data": {"title": "Home", "menu": "home", "pro": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (13)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (13)</li><li>Contents (13)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>ucode</th><th>procode</th><th>orgcode</th><th>name</th><th>description</th><th>pro_date</th><th>pro_update_at</th><th>pro_update_by</th><th>mapping</th><th>fund</th><th>country</th><th>province</th><th>district</th><th>llg</th><th>pro_site</th><th>kmlfile</th><th>gps</th><th>lat</th><th>lon</th><th>gps_at</th><th>gps_by</th><th>budget</th><th>budget_at</th><th>budget_by</th><th>payment_total</th><th>payment_at</th><th>payment_by</th><th>pro_officer_id</th><th>pro_officer_name</th><th>pro_officer_scope</th><th>pro_officer_at</th><th>pro_officer_by</th><th>contractor_id</th><th>contractor_code</th><th>contractor_name</th><th>contract_file</th><th>contractor_at</th><th>contractor_by</th><th>create_at</th><th>create_by</th><th>update_at</th><th>update_by</th><th>create_org</th><th>status</th><th>statusnotes</th><th>status_at</th><th>status_by</th><th>pro_officer_cert</th><th>pro_contractor_cert</th><th>pro_officer_cert_at</th><th>pro_officer_cert_by</th><th>pro_contractor_cert_at</th><th>pro_contractor_cert_by</th><th>evaluation_file</th><th>evaluation_notes</th><th>evaluation_date</th><th>evaluation_by</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">15</td><td title=\"string (23)\">64c9bd1b0b9031690942747</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (12)\">Cook Project</td><td title=\"string (49)\">This is cook project this will go down to history</td><td title=\"string (10)\">2023-10-31</td><td title=\"string (19)\">2024-02-21 15:07:08</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (3)\">pip</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1404</td><td title=\"string (7)\">PG14046</td><td title=\"string (30)\">Hawain Bridge, Kurakum Village</td><td title=\"string (49)\">public/uploads/gps_files/142023-01_1705525853.kml</td><td title=\"string (20)\">-3.577419,143.640458</td><td title=\"string (9)\">-3.577419</td><td title=\"string (10)\">143.640458</td><td title=\"string (19)\">2024-03-12 15:30:25</td><td title=\"string (7)\">Dok Man</td><td title=\"string (7)\">5679.00</td><td title=\"string (19)\">2024-01-18 12:52:35</td><td title=\"string (5)\">Minad</td><td title=\"string (8)\">20812.13</td><td title=\"string (19)\">2024-03-12 15:26:05</td><td title=\"string (7)\">Dok Man</td><td title=\"string (2)\">11</td><td title=\"string (7)\">Dok Man</td><td title=\"string (33)\">This is the scome of work for him</td><td title=\"string (19)\">2024-01-18 12:52:12</td><td title=\"string (5)\">Minad</td><td title=\"string (2)\">17</td><td title=\"string (6)\">144082</td><td title=\"string (17)\">Wonder Contractor</td><td title=\"string (62)\">public/uploads/contract_files/confile_142023-01_1705470291.pdf</td><td title=\"string (19)\">2024-01-17 15:44:51</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-08-02 12:19:07</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-03-12 15:30:25</td><td title=\"string (7)\">Dok Man</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (38)\">It's been activated and work continues</td><td title=\"string (19)\">2024-01-17 14:45:08</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>1</th><td title=\"string (2)\">16</td><td title=\"string (23)\">64cafa3b45a461691023931</td><td title=\"string (9)\">142023-02</td><td title=\"string (4)\">2345</td><td title=\"string (17)\">Wondering Project</td><td title=\"string (29)\">This is the wondering project</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (2)\">tu</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1402</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (18)\">-5.85549, 147.4178</td><td title=\"string (9)\">-3.664039</td><td title=\"string (10)\">143.372469</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (4)\">0.00</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (2)\">11</td><td title=\"string (7)\">Dok Man</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (19)\">2023-08-03 10:52:11</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-23 10:05:58</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (9)\">completed</td><td title=\"string (23)\">Completed on 12-10-2023</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>2</th><td title=\"string (2)\">19</td><td title=\"string (23)\">654af251245c01699410513</td><td title=\"string (9)\">142023-05</td><td title=\"string (4)\">2345</td><td title=\"string (23)\">ProWan Cool LLG Project</td><td title=\"string (32)\">ProWan Project is a cool project</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (3)\">PIP</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1404</td><td title=\"string (7)\">PG14044</td><td title=\"string (0)\"></td><td title=\"string (49)\">public/uploads/gps_files/142023-05_1699484983.kml</td><td title=\"string (20)\">-3.868966,143.023704</td><td title=\"string (9)\">-3.868966</td><td title=\"string (10)\">143.023704</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (9)\">400000.00</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (9)\">100000.00</td><td title=\"string (19)\">2024-02-13 11:55:49</td><td title=\"string (5)\">Minad</td><td title=\"string (2)\">11</td><td title=\"string (7)\">Dok Man</td><td title=\"string (42)\">Identify Works Civil Works and Report Back</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (2)\">16</td><td title=\"string (6)\">145259</td><td title=\"string (7)\">Turbeng</td><td title=\"string (62)\">public/uploads/contract_files/confile_142023-05_1699485182.pdf</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (19)\">2023-11-08 12:28:33</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-13 11:55:49</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>3</th><td title=\"string (2)\">20</td><td title=\"string (23)\">654af3ea833c71699410922</td><td title=\"string (9)\">142023-06</td><td title=\"string (4)\">2345</td><td title=\"string (12)\">Ori Projects</td><td title=\"string (19)\">This is ori project</td><td title=\"string (10)\">2023-09-26</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (2)\">eu</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1404</td><td title=\"string (7)\">PG14042</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (21)\">-6.786405, 145.721189</td><td title=\"string (9)\">-6.786405</td><td title=\"string (10)\">145.721189</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (8)\">45567.00</td><td title=\"string (19)\">2024-02-05 12:35:38</td><td title=\"string (5)\">Minad</td><td title=\"string (4)\">0.00</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (2)\">17</td><td title=\"string (6)\">144082</td><td title=\"string (17)\">Wonder Contractor</td><td title=\"string (62)\">public/uploads/contract_files/confile_142023-06_1707100686.pdf</td><td title=\"string (19)\">2024-02-05 12:38:06</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-11-08 12:35:22</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-22 17:11:56</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (8)\">canceled</td><td title=\"string (50)\">Project was cancelled due to financial constraints</td><td title=\"string (19)\">2024-02-05 12:37:46</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>4</th><td title=\"string (2)\">21</td><td title=\"string (23)\">654af93ca86e81699412284</td><td title=\"string (9)\">142023-07</td><td title=\"string (4)\">2345</td><td title=\"string (9)\">Ring Dong</td><td title=\"string (7)\">This i </td><td title=\"string (10)\">2024-02-01</td><td title=\"string (19)\">2024-02-13 11:04:40</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (2)\">eu</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1404</td><td title=\"string (7)\">PG14044</td><td title=\"string (0)\"></td><td title=\"string (49)\">public/uploads/gps_files/142023-07_1705900840.kml</td><td title=\"string (21)\">-3.757755, 143.493639</td><td title=\"string (9)\">-3.757755</td><td title=\"string (11)\"> 143.493639</td><td title=\"string (19)\">2024-02-13 10:55:40</td><td title=\"string (5)\">Minad</td><td title=\"string (8)\">20500.00</td><td title=\"string (19)\">2024-02-13 10:57:06</td><td title=\"string (5)\">Minad</td><td title=\"string (7)\">4528.43</td><td title=\"string (19)\">2024-02-13 11:03:29</td><td title=\"string (5)\">Minad</td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (2)\">16</td><td title=\"string (6)\">145259</td><td title=\"string (7)\">Turbeng</td><td title=\"string (62)\">public/uploads/contract_files/confile_142023-07_1699484715.pdf</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (19)\">2023-11-08 12:58:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-13 11:04:40</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>5</th><td title=\"string (2)\">27</td><td title=\"string (23)\">65a7325918c401705456217</td><td title=\"string (10)\">202414-001</td><td title=\"string (4)\">2345</td><td title=\"string (6)\">Wanajo</td><td title=\"string (36)\">This is the project about Wanajoring</td><td title=\"string (10)\">2024-01-12</td><td title=\"string (19)\">2024-01-17 11:50:17</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (3)\">pip</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1402</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (50)\">public/uploads/gps_files/202414-001_1705892445.kml</td><td title=\"string (20)\">-3.559390,143.622710</td><td title=\"string (9)\">-3.559390</td><td title=\"string (10)\">143.622710</td><td title=\"string (19)\">2024-01-22 13:01:49</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">24.00</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (5)\">30.00</td><td title=\"string (19)\">2024-01-19 14:18:27</td><td title=\"string (5)\">Minad</td><td title=\"string (2)\">12</td><td title=\"string (7)\">Wan Boy</td><td title=\"string (16)\">Asses the scope </td><td title=\"string (19)\">2024-01-17 12:38:19</td><td title=\"string (5)\">Minad</td><td title=\"string (2)\">17</td><td title=\"string (6)\">144082</td><td title=\"string (17)\">Wonder Contractor</td><td title=\"string (63)\">public/uploads/contract_files/confile_202414-001_1705456582.pdf</td><td title=\"string (19)\">2024-01-17 11:56:22</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-01-17 11:50:17</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-13 10:41:38</td><td title=\"string (5)\">Minad</td><td title=\"string (17)\">Figure Out Oorgee</td><td title=\"string (9)\">completed</td><td title=\"string (9)\">This hold</td><td title=\"string (19)\">2024-01-17 12:42:29</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>6</th><td title=\"string (2)\">28</td><td title=\"string (23)\">65caaa0b71ab31707780619</td><td title=\"string (21)\">PG14045-2345-2024-001</td><td title=\"string (4)\">2345</td><td title=\"string (11)\">Project Col</td><td title=\"string (0)\"></td><td title=\"string (10)\">2024-02-08</td><td title=\"string (19)\">2024-02-13 09:31:46</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (2)\">eu</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1404</td><td title=\"string (7)\">PG14045</td><td title=\"string (30)\">Hawain Bridge, Kurakum Village</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (7)\">4230.00</td><td title=\"string (19)\">2024-02-13 10:30:34</td><td title=\"string (5)\">Minad</td><td title=\"string (4)\">0.00</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (2)\">10</td><td title=\"string (7)\">Pik man</td><td title=\"string (33)\">To assist to check off milestones</td><td title=\"string (19)\">2024-02-13 10:31:18</td><td title=\"string (5)\">Minad</td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (19)\">2024-02-13 09:30:19</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-13 10:31:18</td><td title=\"string (5)\">Minad</td><td title=\"string (36)\">East Sepik Provincial Administration</td><td title=\"string (6)\">active</td><td title=\"string (17)\">Project is active</td><td title=\"string (19)\">2024-02-13 10:30:26</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>7</th><td title=\"string (2)\">29</td><td title=\"string (23)\">65cac2e398bca1707786979</td><td title=\"string (19)\">PG14044-2345-2024-1</td><td title=\"string (4)\">2345</td><td title=\"string (15)\">Boe Wan Project</td><td title=\"string (24)\">This is the boel project</td><td title=\"string (10)\">2023-12-12</td><td title=\"string (19)\">2024-02-13 11:16:19</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (2)\">tu</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1404</td><td title=\"string (7)\">PG14044</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (4)\">0.00</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (19)\">2024-02-13 11:16:19</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-23 10:06:05</td><td title=\"string (5)\">Minad</td><td title=\"string (36)\">East Sepik Provincial Administration</td><td title=\"string (6)\">active</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>8</th><td title=\"string (2)\">30</td><td title=\"string (23)\">65d15d3d65c061708219709</td><td title=\"string (19)\">PG14045-2345-202459</td><td title=\"string (4)\">2345</td><td title=\"string (11)\">Cook Groups</td><td title=\"string (22)\">This is a cook project</td><td title=\"string (10)\">2024-01-31</td><td title=\"string (19)\">2024-02-18 11:28:29</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (2)\">go</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1404</td><td title=\"string (7)\">PG14045</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (4)\">0.00</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (19)\">2024-02-18 11:28:29</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-23 10:06:11</td><td title=\"string (5)\">Minad</td><td title=\"string (36)\">East Sepik Provincial Administration</td><td title=\"string (6)\">active</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>9</th><td title=\"string (2)\">31</td><td title=\"string (23)\">65d15d61f18f81708219745</td><td title=\"string (19)\">PG14044-2345-202459</td><td title=\"string (4)\">2345</td><td title=\"string (12)\">Testa Dakoii</td><td title=\"string (0)\"></td><td title=\"string (10)\">2024-02-21</td><td title=\"string (19)\">2024-02-18 11:29:05</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (2)\">hi</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1404</td><td title=\"string (7)\">PG14044</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (4)\">0.00</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (19)\">2024-02-18 11:29:05</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-23 10:06:15</td><td title=\"string (5)\">Minad</td><td title=\"string (36)\">East Sepik Provincial Administration</td><td title=\"string (6)\">active</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>10</th><td title=\"string (2)\">32</td><td title=\"string (23)\">65d15d8a2989e1708219786</td><td title=\"string (19)\">PG14046-2345-2024-1</td><td title=\"string (4)\">2345</td><td title=\"string (14)\">Terima Cooking</td><td title=\"string (20)\">This si teri cooking</td><td title=\"string (10)\">2024-03-09</td><td title=\"string (19)\">2024-02-18 11:29:46</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (2)\">gg</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1404</td><td title=\"string (7)\">PG14046</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (4)\">0.00</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (19)\">2024-02-18 11:29:46</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-23 10:06:19</td><td title=\"string (5)\">Minad</td><td title=\"string (36)\">East Sepik Provincial Administration</td><td title=\"string (6)\">active</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>11</th><td title=\"string (2)\">33</td><td title=\"string (23)\">65d19a44368ee1708235332</td><td title=\"string (18)\">PG14044-2345-20243</td><td title=\"string (4)\">2345</td><td title=\"string (8)\">Retrance</td><td title=\"string (16)\">This is retrance</td><td title=\"string (10)\">2024-02-06</td><td title=\"string (19)\">2024-02-18 15:48:52</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"string (2)\">we</td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1404</td><td title=\"string (7)\">PG14044</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (4)\">0.00</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (19)\">2024-02-18 15:48:52</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-23 10:06:22</td><td title=\"string (5)\">Minad</td><td title=\"string (36)\">East Sepik Provincial Administration</td><td title=\"string (6)\">active</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr><tr><th>12</th><td title=\"string (2)\">34</td><td title=\"string (23)\">6728501eb6bb81730695198</td><td title=\"string (14)\">PG14045-2345-3</td><td title=\"string (4)\">2345</td><td title=\"string (12)\">Wewak Hydro </td><td title=\"string (0)\"></td><td title=\"string (10)\">2024-11-01</td><td title=\"string (19)\">2024-11-04 14:39:58</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (2)\">PG</td><td title=\"string (4)\">PG14</td><td title=\"string (6)\">PG1404</td><td title=\"string (7)\">PG14045</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (4)\">0.00</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (19)\">2024-11-04 14:39:58</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-11-04 14:39:58</td><td title=\"string (5)\">Minad</td><td title=\"string (36)\">East Sepik Provincial Administration</td><td title=\"string (6)\">active</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"64c9bd1b0b9031690942747\"<div class=\"access-path\">$value[0]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[0]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[0]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (12) \"Cook Project\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (49) \"This is cook project this will go down to history\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>string</var> (10) \"2023-10-31\"<div class=\"access-path\">$value[0]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-21 15:07:08\"<div class=\"access-path\">$value[0]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (3) \"pip\"<div class=\"access-path\">$value[0]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[0]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[0]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1404\"<div class=\"access-path\">$value[0]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (7) \"PG14046\"<div class=\"access-path\">$value[0]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (30) \"Hawain Bridge, Kurakum Village\"<div class=\"access-path\">$value[0]['pro_site']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>kmlfile</dfn> =&gt; <var>string</var> (49) \"public/uploads/gps_files/142023-01_1705525853.kml\"<div class=\"access-path\">$value[0]['kmlfile']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (8.1KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.1KB Jan 18 2024 public/uploads/gps_files/142023-01_1705525853.kml\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (20) \"-3.577419,143.640458\"<div class=\"access-path\">$value[0]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (9) \"-3.577419\"<div class=\"access-path\">$value[0]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (10) \"143.640458\"<div class=\"access-path\">$value[0]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>string</var> (19) \"2024-03-12 15:30:25\"<div class=\"access-path\">$value[0]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[0]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>string</var> (7) \"5679.00\"<div class=\"access-path\">$value[0]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 12:52:35\"<div class=\"access-path\">$value[0]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (8) \"20812.13\"<div class=\"access-path\">$value[0]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>string</var> (19) \"2024-03-12 15:26:05\"<div class=\"access-path\">$value[0]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[0]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[0]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[0]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (33) \"This is the scome of work for him\"<div class=\"access-path\">$value[0]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 12:52:12\"<div class=\"access-path\">$value[0]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[0]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (6) \"144082\"<div class=\"access-path\">$value[0]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (17) \"Wonder Contractor\"<div class=\"access-path\">$value[0]['contractor_name']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>contract_file</dfn> =&gt; <var>string</var> (62) \"public/uploads/contract_files/confile_142023-01_1705470291.pdf\"<div class=\"access-path\">$value[0]['contract_file']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (29.2KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.2KB Jan 17 2024 public/uploads/contract_files/confile_142023-01_1705470291.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>string</var> (19) \"2024-01-17 15:44:51\"<div class=\"access-path\">$value[0]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-08-02 12:19:07\"<div class=\"access-path\">$value[0]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-03-12 15:30:25\"<div class=\"access-path\">$value[0]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[0]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (38) \"It's been activated and work continues\"<div class=\"access-path\">$value[0]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-01-17 14:45:08\"<div class=\"access-path\">$value[0]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"64cafa3b45a461691023931\"<div class=\"access-path\">$value[1]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-02\"<div class=\"access-path\">$value[1]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[1]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (17) \"Wondering Project\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (29) \"This is the wondering project\"<div class=\"access-path\">$value[1]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (2) \"tu\"<div class=\"access-path\">$value[1]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[1]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[1]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1402\"<div class=\"access-path\">$value[1]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['pro_site']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>kmlfile</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['kmlfile']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (18) \"-5.85549, 147.4178\"<div class=\"access-path\">$value[1]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (9) \"-3.664039\"<div class=\"access-path\">$value[1]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (10) \"143.372469\"<div class=\"access-path\">$value[1]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[1]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[1]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[1]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['contractor_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contract_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['contract_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-08-03 10:52:11\"<div class=\"access-path\">$value[1]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-23 10:05:58\"<div class=\"access-path\">$value[1]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (9) \"completed\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (23) \"Completed on 12-10-2023\"<div class=\"access-path\">$value[1]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"654af251245c01699410513\"<div class=\"access-path\">$value[2]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-05\"<div class=\"access-path\">$value[2]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[2]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (23) \"ProWan Cool LLG Project\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (32) \"ProWan Project is a cool project\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (3) \"PIP\"<div class=\"access-path\">$value[2]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[2]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[2]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1404\"<div class=\"access-path\">$value[2]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (7) \"PG14044\"<div class=\"access-path\">$value[2]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['pro_site']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>kmlfile</dfn> =&gt; <var>string</var> (49) \"public/uploads/gps_files/142023-05_1699484983.kml\"<div class=\"access-path\">$value[2]['kmlfile']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (8.1KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.1KB Nov 09 2023 public/uploads/gps_files/142023-05_1699484983.kml\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (20) \"-3.868966,143.023704\"<div class=\"access-path\">$value[2]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (9) \"-3.868966\"<div class=\"access-path\">$value[2]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (10) \"143.023704\"<div class=\"access-path\">$value[2]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>string</var> (9) \"400000.00\"<div class=\"access-path\">$value[2]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (9) \"100000.00\"<div class=\"access-path\">$value[2]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:55:49\"<div class=\"access-path\">$value[2]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[2]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[2]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (42) \"Identify Works Civil Works and Report Back\"<div class=\"access-path\">$value[2]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[2]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (6) \"145259\"<div class=\"access-path\">$value[2]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (7) \"Turbeng\"<div class=\"access-path\">$value[2]['contractor_name']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>contract_file</dfn> =&gt; <var>string</var> (62) \"public/uploads/contract_files/confile_142023-05_1699485182.pdf\"<div class=\"access-path\">$value[2]['contract_file']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (219.5KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.5KB Nov 09 2023 public/uploads/contract_files/confile_142023-05_1699485182.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-11-08 12:28:33\"<div class=\"access-path\">$value[2]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:55:49\"<div class=\"access-path\">$value[2]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"20\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"654af3ea833c71699410922\"<div class=\"access-path\">$value[3]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-06\"<div class=\"access-path\">$value[3]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[3]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (12) \"Ori Projects\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (19) \"This is ori project\"<div class=\"access-path\">$value[3]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>string</var> (10) \"2023-09-26\"<div class=\"access-path\">$value[3]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (2) \"eu\"<div class=\"access-path\">$value[3]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[3]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[3]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1404\"<div class=\"access-path\">$value[3]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (7) \"PG14042\"<div class=\"access-path\">$value[3]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['pro_site']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>kmlfile</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['kmlfile']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (21) \"-6.786405, 145.721189\"<div class=\"access-path\">$value[3]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (9) \"-6.786405\"<div class=\"access-path\">$value[3]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (10) \"145.721189\"<div class=\"access-path\">$value[3]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>string</var> (8) \"45567.00\"<div class=\"access-path\">$value[3]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>string</var> (19) \"2024-02-05 12:35:38\"<div class=\"access-path\">$value[3]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[3]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[3]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[3]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (6) \"144082\"<div class=\"access-path\">$value[3]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (17) \"Wonder Contractor\"<div class=\"access-path\">$value[3]['contractor_name']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>contract_file</dfn> =&gt; <var>string</var> (62) \"public/uploads/contract_files/confile_142023-06_1707100686.pdf\"<div class=\"access-path\">$value[3]['contract_file']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (192.8KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.8KB Feb 05 2024 public/uploads/contract_files/confile_142023-06_1707100686.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>string</var> (19) \"2024-02-05 12:38:06\"<div class=\"access-path\">$value[3]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-11-08 12:35:22\"<div class=\"access-path\">$value[3]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-22 17:11:56\"<div class=\"access-path\">$value[3]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (8) \"canceled\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (50) \"Project was cancelled due to financial constraints\"<div class=\"access-path\">$value[3]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-02-05 12:37:46\"<div class=\"access-path\">$value[3]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"654af93ca86e81699412284\"<div class=\"access-path\">$value[4]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-07\"<div class=\"access-path\">$value[4]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[4]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (9) \"Ring Dong\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (7) \"This i \"<div class=\"access-path\">$value[4]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>string</var> (10) \"2024-02-01\"<div class=\"access-path\">$value[4]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:04:40\"<div class=\"access-path\">$value[4]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (2) \"eu\"<div class=\"access-path\">$value[4]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[4]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[4]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1404\"<div class=\"access-path\">$value[4]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (7) \"PG14044\"<div class=\"access-path\">$value[4]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['pro_site']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>kmlfile</dfn> =&gt; <var>string</var> (49) \"public/uploads/gps_files/142023-07_1705900840.kml\"<div class=\"access-path\">$value[4]['kmlfile']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (377KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 22 2024 public/uploads/gps_files/142023-07_1705900840.kml\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (21) \"-3.757755, 143.493639\"<div class=\"access-path\">$value[4]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (9) \"-3.757755\"<div class=\"access-path\">$value[4]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (11) \" 143.493639\"<div class=\"access-path\">$value[4]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 10:55:40\"<div class=\"access-path\">$value[4]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>string</var> (8) \"20500.00\"<div class=\"access-path\">$value[4]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 10:57:06\"<div class=\"access-path\">$value[4]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (7) \"4528.43\"<div class=\"access-path\">$value[4]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:03:29\"<div class=\"access-path\">$value[4]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[4]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[4]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (6) \"145259\"<div class=\"access-path\">$value[4]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (7) \"Turbeng\"<div class=\"access-path\">$value[4]['contractor_name']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>contract_file</dfn> =&gt; <var>string</var> (62) \"public/uploads/contract_files/confile_142023-07_1699484715.pdf\"<div class=\"access-path\">$value[4]['contract_file']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (192.8KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.8KB Nov 09 2023 public/uploads/contract_files/confile_142023-07_1699484715.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-11-08 12:58:04\"<div class=\"access-path\">$value[4]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:04:40\"<div class=\"access-path\">$value[4]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"27\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65a7325918c401705456217\"<div class=\"access-path\">$value[5]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (10) \"202414-001\"<div class=\"access-path\">$value[5]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[5]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (6) \"Wanajo\"<div class=\"access-path\">$value[5]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (36) \"This is the project about Wanajoring\"<div class=\"access-path\">$value[5]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>string</var> (10) \"2024-01-12\"<div class=\"access-path\">$value[5]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>string</var> (19) \"2024-01-17 11:50:17\"<div class=\"access-path\">$value[5]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (3) \"pip\"<div class=\"access-path\">$value[5]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[5]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[5]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1402\"<div class=\"access-path\">$value[5]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['pro_site']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>kmlfile</dfn> =&gt; <var>string</var> (50) \"public/uploads/gps_files/202414-001_1705892445.kml\"<div class=\"access-path\">$value[5]['kmlfile']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (31.1KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.1KB Jan 22 2024 public/uploads/gps_files/202414-001_1705892445.kml\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (20) \"-3.559390,143.622710\"<div class=\"access-path\">$value[5]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (9) \"-3.559390\"<div class=\"access-path\">$value[5]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (10) \"143.622710\"<div class=\"access-path\">$value[5]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>string</var> (19) \"2024-01-22 13:01:49\"<div class=\"access-path\">$value[5]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>string</var> (5) \"24.00\"<div class=\"access-path\">$value[5]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (5) \"30.00\"<div class=\"access-path\">$value[5]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>string</var> (19) \"2024-01-19 14:18:27\"<div class=\"access-path\">$value[5]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[5]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (7) \"Wan Boy\"<div class=\"access-path\">$value[5]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (16) \"Asses the scope \"<div class=\"access-path\">$value[5]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>string</var> (19) \"2024-01-17 12:38:19\"<div class=\"access-path\">$value[5]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[5]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (6) \"144082\"<div class=\"access-path\">$value[5]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (17) \"Wonder Contractor\"<div class=\"access-path\">$value[5]['contractor_name']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>contract_file</dfn> =&gt; <var>string</var> (63) \"public/uploads/contract_files/confile_202414-001_1705456582.pdf\"<div class=\"access-path\">$value[5]['contract_file']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (29.2KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.2KB Jan 17 2024 public/uploads/contract_files/confile_202414-001_1705456582.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>string</var> (19) \"2024-01-17 11:56:22\"<div class=\"access-path\">$value[5]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-01-17 11:50:17\"<div class=\"access-path\">$value[5]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 10:41:38\"<div class=\"access-path\">$value[5]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (17) \"Figure Out Oorgee\"<div class=\"access-path\">$value[5]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (9) \"completed\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (9) \"This hold\"<div class=\"access-path\">$value[5]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-01-17 12:42:29\"<div class=\"access-path\">$value[5]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65caaa0b71ab31707780619\"<div class=\"access-path\">$value[6]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (21) \"PG14045-2345-2024-001\"<div class=\"access-path\">$value[6]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[6]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (11) \"Project Col\"<div class=\"access-path\">$value[6]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>string</var> (10) \"2024-02-08\"<div class=\"access-path\">$value[6]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 09:31:46\"<div class=\"access-path\">$value[6]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (2) \"eu\"<div class=\"access-path\">$value[6]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[6]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[6]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1404\"<div class=\"access-path\">$value[6]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (7) \"PG14045\"<div class=\"access-path\">$value[6]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (30) \"Hawain Bridge, Kurakum Village\"<div class=\"access-path\">$value[6]['pro_site']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>kmlfile</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['kmlfile']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>string</var> (7) \"4230.00\"<div class=\"access-path\">$value[6]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 10:30:34\"<div class=\"access-path\">$value[6]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[6]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[6]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (7) \"Pik man\"<div class=\"access-path\">$value[6]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (33) \"To assist to check off milestones\"<div class=\"access-path\">$value[6]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 10:31:18\"<div class=\"access-path\">$value[6]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[6]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['contractor_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contract_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['contract_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 09:30:19\"<div class=\"access-path\">$value[6]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 10:31:18\"<div class=\"access-path\">$value[6]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (36) \"East Sepik Provincial Administration\"<div class=\"access-path\">$value[6]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[6]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (17) \"Project is active\"<div class=\"access-path\">$value[6]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 10:30:26\"<div class=\"access-path\">$value[6]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[7]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65cac2e398bca1707786979\"<div class=\"access-path\">$value[7]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (19) \"PG14044-2345-2024-1\"<div class=\"access-path\">$value[7]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[7]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Boe Wan Project\"<div class=\"access-path\">$value[7]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (24) \"This is the boel project\"<div class=\"access-path\">$value[7]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>string</var> (10) \"2023-12-12\"<div class=\"access-path\">$value[7]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:16:19\"<div class=\"access-path\">$value[7]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[7]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (2) \"tu\"<div class=\"access-path\">$value[7]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[7]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[7]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1404\"<div class=\"access-path\">$value[7]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (7) \"PG14044\"<div class=\"access-path\">$value[7]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['pro_site']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>kmlfile</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['kmlfile']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[7]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[7]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[7]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['contractor_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contract_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['contract_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:16:19\"<div class=\"access-path\">$value[7]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[7]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-23 10:06:05\"<div class=\"access-path\">$value[7]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[7]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (36) \"East Sepik Provincial Administration\"<div class=\"access-path\">$value[7]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[7]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[8]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65d15d3d65c061708219709\"<div class=\"access-path\">$value[8]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (19) \"PG14045-2345-202459\"<div class=\"access-path\">$value[8]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[8]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (11) \"Cook Groups\"<div class=\"access-path\">$value[8]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (22) \"This is a cook project\"<div class=\"access-path\">$value[8]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>string</var> (10) \"2024-01-31\"<div class=\"access-path\">$value[8]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-18 11:28:29\"<div class=\"access-path\">$value[8]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[8]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (2) \"go\"<div class=\"access-path\">$value[8]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[8]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[8]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1404\"<div class=\"access-path\">$value[8]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (7) \"PG14045\"<div class=\"access-path\">$value[8]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['pro_site']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>kmlfile</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['kmlfile']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[8]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[8]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[8]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['contractor_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contract_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['contract_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-18 11:28:29\"<div class=\"access-path\">$value[8]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[8]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-23 10:06:11\"<div class=\"access-path\">$value[8]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[8]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (36) \"East Sepik Provincial Administration\"<div class=\"access-path\">$value[8]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[8]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"31\"<div class=\"access-path\">$value[9]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65d15d61f18f81708219745\"<div class=\"access-path\">$value[9]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (19) \"PG14044-2345-202459\"<div class=\"access-path\">$value[9]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[9]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (12) \"Testa Dakoii\"<div class=\"access-path\">$value[9]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>string</var> (10) \"2024-02-21\"<div class=\"access-path\">$value[9]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-18 11:29:05\"<div class=\"access-path\">$value[9]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[9]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (2) \"hi\"<div class=\"access-path\">$value[9]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[9]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[9]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1404\"<div class=\"access-path\">$value[9]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (7) \"PG14044\"<div class=\"access-path\">$value[9]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['pro_site']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>kmlfile</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['kmlfile']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[9]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[9]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[9]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['contractor_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contract_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['contract_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-18 11:29:05\"<div class=\"access-path\">$value[9]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[9]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-23 10:06:15\"<div class=\"access-path\">$value[9]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[9]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (36) \"East Sepik Provincial Administration\"<div class=\"access-path\">$value[9]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[9]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"32\"<div class=\"access-path\">$value[10]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65d15d8a2989e1708219786\"<div class=\"access-path\">$value[10]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (19) \"PG14046-2345-2024-1\"<div class=\"access-path\">$value[10]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[10]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Terima Cooking\"<div class=\"access-path\">$value[10]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (20) \"This si teri cooking\"<div class=\"access-path\">$value[10]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>string</var> (10) \"2024-03-09\"<div class=\"access-path\">$value[10]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-18 11:29:46\"<div class=\"access-path\">$value[10]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[10]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (2) \"gg\"<div class=\"access-path\">$value[10]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[10]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[10]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1404\"<div class=\"access-path\">$value[10]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (7) \"PG14046\"<div class=\"access-path\">$value[10]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['pro_site']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>kmlfile</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['kmlfile']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[10]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[10]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[10]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['contractor_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contract_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['contract_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-18 11:29:46\"<div class=\"access-path\">$value[10]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[10]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-23 10:06:19\"<div class=\"access-path\">$value[10]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[10]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (36) \"East Sepik Provincial Administration\"<div class=\"access-path\">$value[10]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[10]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"33\"<div class=\"access-path\">$value[11]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65d19a44368ee1708235332\"<div class=\"access-path\">$value[11]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (18) \"PG14044-2345-20243\"<div class=\"access-path\">$value[11]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[11]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (8) \"Retrance\"<div class=\"access-path\">$value[11]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (16) \"This is retrance\"<div class=\"access-path\">$value[11]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>string</var> (10) \"2024-02-06\"<div class=\"access-path\">$value[11]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-18 15:48:52\"<div class=\"access-path\">$value[11]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[11]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>string</var> (2) \"we\"<div class=\"access-path\">$value[11]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[11]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[11]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1404\"<div class=\"access-path\">$value[11]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (7) \"PG14044\"<div class=\"access-path\">$value[11]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['pro_site']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>kmlfile</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['kmlfile']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[11]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[11]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[11]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['contractor_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contract_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['contract_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-18 15:48:52\"<div class=\"access-path\">$value[11]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[11]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-23 10:06:22\"<div class=\"access-path\">$value[11]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[11]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (36) \"East Sepik Provincial Administration\"<div class=\"access-path\">$value[11]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[11]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['evaluation_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>12</dfn> =&gt; <var>array</var> (58)<div class=\"access-path\">$value[12]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"34\"<div class=\"access-path\">$value[12]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"6728501eb6bb81730695198\"<div class=\"access-path\">$value[12]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (14) \"PG14045-2345-3\"<div class=\"access-path\">$value[12]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[12]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (12) \"Wewak Hydro \"<div class=\"access-path\">$value[12]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_date</dfn> =&gt; <var>string</var> (10) \"2024-11-01\"<div class=\"access-path\">$value[12]['pro_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_at</dfn> =&gt; <var>string</var> (19) \"2024-11-04 14:39:58\"<div class=\"access-path\">$value[12]['pro_update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[12]['pro_update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>mapping</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['mapping']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>fund</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['fund']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value[12]['country']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>province</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[12]['province']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>district</dfn> =&gt; <var>string</var> (6) \"PG1404\"<div class=\"access-path\">$value[12]['district']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>llg</dfn> =&gt; <var>string</var> (7) \"PG14045\"<div class=\"access-path\">$value[12]['llg']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_site</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['pro_site']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>kmlfile</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['kmlfile']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['gps']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lat</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['lat']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>lon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['lon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['gps_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>gps_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['gps_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['budget']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['budget_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>budget_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['budget_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_total</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[12]['payment_total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['payment_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>payment_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['payment_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[12]['pro_officer_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['pro_officer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_scope</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['pro_officer_scope']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['pro_officer_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['pro_officer_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_id</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[12]['contractor_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_code</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['contractor_code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_name</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['contractor_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contract_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['contract_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['contractor_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>contractor_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['contractor_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-11-04 14:39:58\"<div class=\"access-path\">$value[12]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[12]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-11-04 14:39:58\"<div class=\"access-path\">$value[12]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[12]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_org</dfn> =&gt; <var>string</var> (36) \"East Sepik Provincial Administration\"<div class=\"access-path\">$value[12]['create_org']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[12]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>statusnotes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['statusnotes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['pro_officer_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['pro_contractor_cert']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['pro_officer_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_officer_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['pro_officer_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_at</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['pro_contractor_cert_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>pro_contractor_cert_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['pro_contractor_cert_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_file</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['evaluation_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['evaluation_notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['evaluation_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>evaluation_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['evaluation_by']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "milestones": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (7)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (7)</li><li>Contents (7)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>ucode</th><th>procode</th><th>orgcode</th><th>milestones</th><th>checked</th><th>checked_date</th><th>notes</th><th>datefrom</th><th>dateto</th><th>phase_id</th><th>create_at</th><th>create_by</th><th>update_at</th><th>update_by</th><th>status</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">27</td><td title=\"string (23)\">64c9d350e7e091690948432</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (11)\">1 Milestone</td><td title=\"string (9)\">completed</td><td title=\"string (10)\">2023-09-09</td><td title=\"string (67)\">This is notes \r\nwe can always write these notes properly. That's it</td><td title=\"string (10)\">2023-05-10</td><td title=\"string (10)\">2023-08-17</td><td title=\"string (2)\">18</td><td title=\"string (19)\">2023-08-02 13:53:52</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-09-11 09:32:14</td><td title=\"string (7)\">Dok Man</td><td title=\"string (0)\"></td></tr><tr><th>1</th><td title=\"string (2)\">28</td><td title=\"string (23)\">64cb1a1bbec6e1691032091</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (18)\">Writing Submission</td><td title=\"string (9)\">completed</td><td title=\"string (10)\">2023-08-30</td><td title=\"string (14)\">Just completed</td><td title=\"string (10)\">2023-05-17</td><td title=\"string (10)\">2023-08-09</td><td title=\"string (2)\">18</td><td title=\"string (19)\">2023-08-03 13:08:11</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-21 14:59:21</td><td title=\"string (7)\">Dok Man</td><td title=\"string (0)\"></td></tr><tr><th>2</th><td title=\"string (2)\">30</td><td title=\"string (23)\">64cb1ab2067271691032242</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (13)\">3rd Milestone</td><td title=\"string (9)\">completed</td><td title=\"string (10)\">2024-03-12</td><td title=\"string (40)\">This is the concern of the set documents</td><td title=\"string (10)\">2023-11-02</td><td title=\"string (10)\">2023-11-23</td><td title=\"string (2)\">18</td><td title=\"string (19)\">2023-08-03 13:10:42</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-03-12 15:27:20</td><td title=\"string (7)\">Dok Man</td><td title=\"string (0)\"></td></tr><tr><th>3</th><td title=\"string (2)\">31</td><td title=\"string (23)\">654acdc2135cb1699401154</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (11)\">Water Study</td><td title=\"string (4)\">hold</td><td title=\"string (10)\">2024-02-14</td><td title=\"string (25)\">This milestone is on hild</td><td title=\"string (10)\">2023-11-03</td><td title=\"string (10)\">2023-11-11</td><td title=\"string (2)\">19</td><td title=\"string (19)\">2023-11-08 09:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-21 15:57:33</td><td title=\"string (7)\">Dok Man</td><td title=\"string (0)\"></td></tr><tr><th>4</th><td title=\"string (2)\">32</td><td title=\"string (23)\">654acddbe9f3a1699401179</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (9)\">Sun Study</td><td title=\"string (7)\">pending</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (2)\">19</td><td title=\"string (19)\">2023-11-08 09:52:59</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-01-17 18:49:24</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td></tr><tr><th>5</th><td title=\"string (2)\">33</td><td title=\"string (23)\">654acde0814c31699401184</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (10)\">Moon Study</td><td title=\"string (7)\">pending</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (10)\">2023-11-02</td><td title=\"string (10)\">2023-11-12</td><td title=\"string (2)\">19</td><td title=\"string (19)\">2023-11-08 09:53:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-11-10 11:25:08</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>6</th><td title=\"string (2)\">52</td><td title=\"string (23)\">65cabd205ffdc1707785504</td><td title=\"string (9)\">142023-07</td><td title=\"string (4)\">2345</td><td title=\"string (11)\">1 Milestone</td><td title=\"string (7)\">pending</td><td title=\"null\"><var>null</var></td><td title=\"string (0)\"></td><td title=\"string (10)\">2024-02-07</td><td title=\"string (10)\">2024-02-18</td><td title=\"string (2)\">25</td><td title=\"string (19)\">2024-02-13 10:51:44</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-13 10:54:18</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"27\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"64c9d350e7e091690948432\"<div class=\"access-path\">$value[0]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[0]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[0]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>milestones</dfn> =&gt; <var>string</var> (11) \"1 Milestone\"<div class=\"access-path\">$value[0]['milestones']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked</dfn> =&gt; <var>string</var> (9) \"completed\"<div class=\"access-path\">$value[0]['checked']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked_date</dfn> =&gt; <var>string</var> (10) \"2023-09-09\"<div class=\"access-path\">$value[0]['checked_date']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>notes</dfn> =&gt; <var>string</var> (67) \"This is notes we can always write these notes properly. That's it\"<div class=\"access-path\">$value[0]['notes']</div></dt><dd><pre>This is notes \r\nwe can always write these notes properly. That's it\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>datefrom</dfn> =&gt; <var>string</var> (10) \"2023-05-10\"<div class=\"access-path\">$value[0]['datefrom']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>dateto</dfn> =&gt; <var>string</var> (10) \"2023-08-17\"<div class=\"access-path\">$value[0]['dateto']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>phase_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[0]['phase_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-08-02 13:53:52\"<div class=\"access-path\">$value[0]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-09-11 09:32:14\"<div class=\"access-path\">$value[0]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[0]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"64cb1a1bbec6e1691032091\"<div class=\"access-path\">$value[1]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[1]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[1]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>milestones</dfn> =&gt; <var>string</var> (18) \"Writing Submission\"<div class=\"access-path\">$value[1]['milestones']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked</dfn> =&gt; <var>string</var> (9) \"completed\"<div class=\"access-path\">$value[1]['checked']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked_date</dfn> =&gt; <var>string</var> (10) \"2023-08-30\"<div class=\"access-path\">$value[1]['checked_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>notes</dfn> =&gt; <var>string</var> (14) \"Just completed\"<div class=\"access-path\">$value[1]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>datefrom</dfn> =&gt; <var>string</var> (10) \"2023-05-17\"<div class=\"access-path\">$value[1]['datefrom']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>dateto</dfn> =&gt; <var>string</var> (10) \"2023-08-09\"<div class=\"access-path\">$value[1]['dateto']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>phase_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[1]['phase_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-08-03 13:08:11\"<div class=\"access-path\">$value[1]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-21 14:59:21\"<div class=\"access-path\">$value[1]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[1]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"64cb1ab2067271691032242\"<div class=\"access-path\">$value[2]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[2]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[2]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>milestones</dfn> =&gt; <var>string</var> (13) \"3rd Milestone\"<div class=\"access-path\">$value[2]['milestones']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked</dfn> =&gt; <var>string</var> (9) \"completed\"<div class=\"access-path\">$value[2]['checked']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked_date</dfn> =&gt; <var>string</var> (10) \"2024-03-12\"<div class=\"access-path\">$value[2]['checked_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>notes</dfn> =&gt; <var>string</var> (40) \"This is the concern of the set documents\"<div class=\"access-path\">$value[2]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>datefrom</dfn> =&gt; <var>string</var> (10) \"2023-11-02\"<div class=\"access-path\">$value[2]['datefrom']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>dateto</dfn> =&gt; <var>string</var> (10) \"2023-11-23\"<div class=\"access-path\">$value[2]['dateto']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>phase_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[2]['phase_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-08-03 13:10:42\"<div class=\"access-path\">$value[2]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-03-12 15:27:20\"<div class=\"access-path\">$value[2]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[2]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"31\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"654acdc2135cb1699401154\"<div class=\"access-path\">$value[3]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[3]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[3]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>milestones</dfn> =&gt; <var>string</var> (11) \"Water Study\"<div class=\"access-path\">$value[3]['milestones']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked</dfn> =&gt; <var>string</var> (4) \"hold\"<div class=\"access-path\">$value[3]['checked']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked_date</dfn> =&gt; <var>string</var> (10) \"2024-02-14\"<div class=\"access-path\">$value[3]['checked_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>notes</dfn> =&gt; <var>string</var> (25) \"This milestone is on hild\"<div class=\"access-path\">$value[3]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>datefrom</dfn> =&gt; <var>string</var> (10) \"2023-11-03\"<div class=\"access-path\">$value[3]['datefrom']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>dateto</dfn> =&gt; <var>string</var> (10) \"2023-11-11\"<div class=\"access-path\">$value[3]['dateto']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>phase_id</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[3]['phase_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-11-08 09:52:34\"<div class=\"access-path\">$value[3]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-21 15:57:33\"<div class=\"access-path\">$value[3]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[3]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"32\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"654acddbe9f3a1699401179\"<div class=\"access-path\">$value[4]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[4]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[4]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>milestones</dfn> =&gt; <var>string</var> (9) \"Sun Study\"<div class=\"access-path\">$value[4]['milestones']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked</dfn> =&gt; <var>string</var> (7) \"pending\"<div class=\"access-path\">$value[4]['checked']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['checked_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>datefrom</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['datefrom']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>dateto</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['dateto']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>phase_id</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[4]['phase_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-11-08 09:52:59\"<div class=\"access-path\">$value[4]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-01-17 18:49:24\"<div class=\"access-path\">$value[4]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"33\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"654acde0814c31699401184\"<div class=\"access-path\">$value[5]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[5]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[5]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>milestones</dfn> =&gt; <var>string</var> (10) \"Moon Study\"<div class=\"access-path\">$value[5]['milestones']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked</dfn> =&gt; <var>string</var> (7) \"pending\"<div class=\"access-path\">$value[5]['checked']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['checked_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>datefrom</dfn> =&gt; <var>string</var> (10) \"2023-11-02\"<div class=\"access-path\">$value[5]['datefrom']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>dateto</dfn> =&gt; <var>string</var> (10) \"2023-11-12\"<div class=\"access-path\">$value[5]['dateto']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>phase_id</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[5]['phase_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-11-08 09:53:04\"<div class=\"access-path\">$value[5]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-11-10 11:25:08\"<div class=\"access-path\">$value[5]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"52\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65cabd205ffdc1707785504\"<div class=\"access-path\">$value[6]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-07\"<div class=\"access-path\">$value[6]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[6]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>milestones</dfn> =&gt; <var>string</var> (11) \"1 Milestone\"<div class=\"access-path\">$value[6]['milestones']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked</dfn> =&gt; <var>string</var> (7) \"pending\"<div class=\"access-path\">$value[6]['checked']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>checked_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['checked_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>datefrom</dfn> =&gt; <var>string</var> (10) \"2024-02-07\"<div class=\"access-path\">$value[6]['datefrom']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>dateto</dfn> =&gt; <var>string</var> (10) \"2024-02-18\"<div class=\"access-path\">$value[6]['dateto']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>phase_id</dfn> =&gt; <var>string</var> (2) \"25\"<div class=\"access-path\">$value[6]['phase_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 10:51:44\"<div class=\"access-path\">$value[6]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 10:54:18\"<div class=\"access-path\">$value[6]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['status']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "payments": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (25)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (25)</li><li>Contents (25)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>ucode</th><th>procode</th><th>orgcode</th><th>amount</th><th>description</th><th>paymentdate</th><th>filepath</th><th>create_at</th><th>create_by</th><th>update_at</th><th>update_by</th><th>status</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (23)\">6422523f243c71679970879</td><td title=\"string (11)\">pip20231401</td><td title=\"string (4)\">2345</td><td title=\"string (4)\">0.00</td><td title=\"string (0)\"></td><td title=\"string (10)\">0000-00-00</td><td title=\"string (0)\"></td><td title=\"string (19)\">2023-03-28 12:34:39</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-03-28 12:34:39</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (23)\">642255e64351c1679971814</td><td title=\"string (10)\">eu20231401</td><td title=\"string (4)\">2345</td><td title=\"string (4)\">0.00</td><td title=\"string (22)\">This is the Road Point</td><td title=\"string (10)\">0000-00-00</td><td title=\"string (0)\"></td><td title=\"string (19)\">2023-03-28 12:50:14</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-03-28 14:27:32</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"string (23)\">64227907a86b81679980807</td><td title=\"string (10)\">eu20231401</td><td title=\"string (4)\">2345</td><td title=\"string (6)\">234.34</td><td title=\"string (31)\">This is the payment for ringkon</td><td title=\"string (10)\">0000-00-00</td><td title=\"string (0)\"></td><td title=\"string (19)\">2023-03-28 15:20:07</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-03-28 15:20:07</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td></tr><tr><th>3</th><td title=\"string (1)\">8</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (4)\">0.00</td><td title=\"string (0)\"></td><td title=\"string (10)\">0000-00-00</td><td title=\"string (87)\">http://localhost/promis/public/uploads/payment_files/paydocs_pip202314-01_168...</td><td title=\"string (19)\">2023-04-01 11:10:35</td><td title=\"string (0)\"></td><td title=\"string (19)\">2023-04-01 11:10:35</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td></tr><tr><th>4</th><td title=\"string (2)\">16</td><td title=\"string (23)\">65a86a2e6b7601705536046</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (5)\">34.00</td><td title=\"string (18)\">Was uploaded later</td><td title=\"string (10)\">2020-11-11</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-01_1705537301.pdf</td><td title=\"string (19)\">2024-01-18 10:00:46</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-01-18 10:21:54</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>5</th><td title=\"string (1)\">4</td><td title=\"string (23)\">6422794e61dae1679980878</td><td title=\"string (10)\">eu20231401</td><td title=\"string (4)\">2345</td><td title=\"string (7)\">2324.45</td><td title=\"string (15)\">This is the cos</td><td title=\"string (10)\">2023-03-03</td><td title=\"string (0)\"></td><td title=\"string (19)\">2023-03-28 15:21:18</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-03-28 15:21:18</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td></tr><tr><th>6</th><td title=\"string (1)\">5</td><td title=\"string (23)\">642282c58f4c31679983301</td><td title=\"string (10)\">eu20231401</td><td title=\"string (4)\">2345</td><td title=\"string (8)\">14345.23</td><td title=\"string (0)\"></td><td title=\"string (10)\">2023-03-08</td><td title=\"string (0)\"></td><td title=\"string (19)\">2023-03-28 16:01:41</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-03-28 16:01:41</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td></tr><tr><th>7</th><td title=\"string (1)\">6</td><td title=\"string (23)\">6426232c70ab01680220972</td><td title=\"string (12)\">pip202314-01</td><td title=\"string (4)\">2345</td><td title=\"string (6)\">458.56</td><td title=\"string (16)\">Front up PAyment</td><td title=\"string (10)\">2023-03-11</td><td title=\"string (0)\"></td><td title=\"string (19)\">2023-03-31 10:02:52</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-04-01 10:22:00</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>8</th><td title=\"string (1)\">7</td><td title=\"string (23)\">64262488a17871680221320</td><td title=\"string (12)\">pip202314-01</td><td title=\"string (4)\">2345</td><td title=\"string (4)\">0.00</td><td title=\"string (24)\">2nd Payment of the rules</td><td title=\"string (10)\">2023-03-13</td><td title=\"string (0)\"></td><td title=\"string (19)\">2023-03-31 10:08:40</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-04-01 11:20:58</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>9</th><td title=\"string (2)\">10</td><td title=\"string (23)\">6427982a1f0fb1680316458</td><td title=\"string (11)\">eu202314-01</td><td title=\"string (4)\">2345</td><td title=\"string (6)\">140.00</td><td title=\"string (11)\">1st Payment</td><td title=\"string (10)\">2023-03-31</td><td title=\"string (87)\">http://localhost/promis/public/uploads/payment_files/paydocs_eu202314-01_1680...</td><td title=\"string (19)\">2023-04-01 12:34:18</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-04-01 12:34:18</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td></tr><tr><th>10</th><td title=\"string (1)\">9</td><td title=\"string (23)\">64278526bc9f31680311590</td><td title=\"string (12)\">pip202314-01</td><td title=\"string (4)\">2345</td><td title=\"string (7)\">1232.00</td><td title=\"string (11)\">This  tokin</td><td title=\"string (10)\">2023-04-05</td><td title=\"string (87)\">http://localhost/promis/public/uploads/payment_files/paydocs_pip202314-01_168...</td><td title=\"string (19)\">2023-04-01 11:13:10</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2023-04-01 11:17:29</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>11</th><td title=\"string (2)\">12</td><td title=\"string (23)\">64f6938c5c1dc1693881228</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (5)\">45.00</td><td title=\"string (36)\">1st Payment to Wanjuwa for the posts</td><td title=\"string (10)\">2023-07-12</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-01_1693881477.pdf</td><td title=\"string (19)\">2023-09-05 12:33:48</td><td title=\"string (7)\">Dok Man</td><td title=\"string (19)\">2024-01-18 10:20:52</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>12</th><td title=\"string (2)\">11</td><td title=\"string (23)\">64c9d1d56931c1690948053</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (5)\">23.23</td><td title=\"string (12)\">Half Payment</td><td title=\"string (10)\">2023-08-02</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-01_1690948053.txt</td><td title=\"string (19)\">2023-08-02 13:47:33</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-01-18 13:57:36</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>13</th><td title=\"string (2)\">13</td><td title=\"string (23)\">654ad71c2d2881699403548</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (5)\">46.00</td><td title=\"string (25)\">This is the third payment</td><td title=\"string (10)\">2023-10-31</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-01_1699403548.csv</td><td title=\"string (19)\">2023-11-08 10:32:28</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-01-18 10:21:01</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>14</th><td title=\"string (2)\">15</td><td title=\"string (23)\">65a868972fbb21705535639</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (5)\">45.00</td><td title=\"string (21)\">This is front Payment</td><td title=\"string (10)\">2023-11-12</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-01_1705535639.pdf</td><td title=\"string (19)\">2024-01-18 09:53:59</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-01-18 12:50:10</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>15</th><td title=\"string (2)\">17</td><td title=\"string (23)\">65a86e6eca1dc1705537134</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (5)\">55.00</td><td title=\"string (16)\">This is new file</td><td title=\"string (10)\">2023-12-12</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-01_1705537181.pdf</td><td title=\"string (19)\">2024-01-18 10:18:54</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-21 15:38:51</td><td title=\"string (7)\">Dok Man</td><td title=\"string (0)\"></td></tr><tr><th>16</th><td title=\"string (2)\">19</td><td title=\"string (23)\">65a9f813510641705637907</td><td title=\"string (10)\">202414-001</td><td title=\"string (4)\">2345</td><td title=\"string (5)\">30.00</td><td title=\"string (22)\">This is an overpayment</td><td title=\"string (10)\">2024-01-04</td><td title=\"string (62)\">public/uploads/payment_files/paydocs_202414-001_1705637907.pdf</td><td title=\"string (19)\">2024-01-19 14:18:27</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-01-19 14:18:27</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td></tr><tr><th>17</th><td title=\"string (2)\">14</td><td title=\"string (23)\">65a7b8c2544881705490626</td><td title=\"string (9)\">142023-02</td><td title=\"string (4)\">2345</td><td title=\"string (4)\">0.00</td><td title=\"string (15)\">This is payment</td><td title=\"string (10)\">2024-01-09</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-02_1705490626.pdf</td><td title=\"string (19)\">2024-01-17 21:23:46</td><td title=\"string (7)\">Dok Man</td><td title=\"string (19)\">2024-01-17 21:49:19</td><td title=\"string (7)\">Dok Man</td><td title=\"string (0)\"></td></tr><tr><th>18</th><td title=\"string (2)\">18</td><td title=\"string (23)\">65a8a0c1c25941705550017</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (6)\">100.00</td><td title=\"string (11)\">Total Payed</td><td title=\"string (10)\">2024-01-09</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-01_1705550017.pdf</td><td title=\"string (19)\">2024-01-18 13:53:37</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-01-18 13:53:37</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td></tr><tr><th>19</th><td title=\"string (2)\">20</td><td title=\"string (23)\">65cabe3a74ad51707785786</td><td title=\"string (9)\">142023-07</td><td title=\"string (4)\">2345</td><td title=\"string (7)\">4325.20</td><td title=\"string (18)\">Up front payment\r\n</td><td title=\"string (10)\">2024-01-31</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-07_1707785786.pdf</td><td title=\"string (19)\">2024-02-13 10:56:26</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-13 11:01:01</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>20</th><td title=\"string (2)\">21</td><td title=\"string (23)\">65cabf9552e171707786133</td><td title=\"string (9)\">142023-07</td><td title=\"string (4)\">2345</td><td title=\"string (6)\">203.23</td><td title=\"string (12)\">Cool payment</td><td title=\"string (10)\">2024-02-08</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-07_1707786133.pdf</td><td title=\"string (19)\">2024-02-13 11:02:13</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-13 11:03:29</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>21</th><td title=\"string (2)\">22</td><td title=\"string (23)\">65cacc25872621707789349</td><td title=\"string (9)\">142023-05</td><td title=\"string (4)\">2345</td><td title=\"string (9)\">100000.00</td><td title=\"string (9)\">NExt pary</td><td title=\"string (10)\">2024-02-08</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-05_1707789349.pdf</td><td title=\"string (19)\">2024-02-13 11:55:49</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-02-13 11:55:49</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td></tr><tr><th>22</th><td title=\"string (2)\">24</td><td title=\"string (23)\">65d58b27369f41708493607</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (5)\">80.90</td><td title=\"string (18)\">This is the timing</td><td title=\"string (10)\">2024-02-14</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-01_1708493607.pdf</td><td title=\"string (19)\">2024-02-21 15:33:27</td><td title=\"string (7)\">Dok Man</td><td title=\"string (19)\">2024-02-21 15:39:20</td><td title=\"string (7)\">Dok Man</td><td title=\"string (0)\"></td></tr><tr><th>23</th><td title=\"string (2)\">23</td><td title=\"string (23)\">65d587484e6fa1708492616</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (5)\">50.00</td><td title=\"string (64)\">This is another follow up payment\r\nFollowing the initial payment</td><td title=\"string (10)\">2024-02-21</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-01_1708494135.pdf</td><td title=\"string (19)\">2024-02-21 15:16:56</td><td title=\"string (7)\">Dok Man</td><td title=\"string (19)\">2024-03-12 15:25:04</td><td title=\"string (7)\">Dok Man</td><td title=\"string (0)\"></td></tr><tr><th>24</th><td title=\"string (2)\">25</td><td title=\"string (23)\">65efe76d0570c1710221165</td><td title=\"string (9)\">142023-01</td><td title=\"string (4)\">2345</td><td title=\"string (8)\">20333.00</td><td title=\"string (32)\">This is one of the payments done</td><td title=\"string (10)\">2024-02-29</td><td title=\"string (61)\">public/uploads/payment_files/paydocs_142023-01_1710221165.pdf</td><td title=\"string (19)\">2024-03-12 15:26:05</td><td title=\"string (7)\">Dok Man</td><td title=\"string (19)\">2024-03-12 15:26:05</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"6422523f243c71679970879\"<div class=\"access-path\">$value[0]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (11) \"pip20231401\"<div class=\"access-path\">$value[0]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[0]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[0]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"0000-00-00\"<div class=\"access-path\">$value[0]['paymentdate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>filepath</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['filepath']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-03-28 12:34:39\"<div class=\"access-path\">$value[0]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-03-28 12:34:39\"<div class=\"access-path\">$value[0]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"642255e64351c1679971814\"<div class=\"access-path\">$value[1]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (10) \"eu20231401\"<div class=\"access-path\">$value[1]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[1]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[1]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (22) \"This is the Road Point\"<div class=\"access-path\">$value[1]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"0000-00-00\"<div class=\"access-path\">$value[1]['paymentdate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>filepath</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['filepath']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-03-28 12:50:14\"<div class=\"access-path\">$value[1]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-03-28 14:27:32\"<div class=\"access-path\">$value[1]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"64227907a86b81679980807\"<div class=\"access-path\">$value[2]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (10) \"eu20231401\"<div class=\"access-path\">$value[2]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[2]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"234.34\"<div class=\"access-path\">$value[2]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (31) \"This is the payment for ringkon\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"0000-00-00\"<div class=\"access-path\">$value[2]['paymentdate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>filepath</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['filepath']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-03-28 15:20:07\"<div class=\"access-path\">$value[2]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-03-28 15:20:07\"<div class=\"access-path\">$value[2]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[3]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"0000-00-00\"<div class=\"access-path\">$value[3]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (87) \"http://localhost/promis/public/uploads/payment_files/paydocs_pip202314-01_16...<div class=\"access-path\">$value[3]['filepath']</div></dt><dd><pre>http://localhost/promis/public/uploads/payment_files/paydocs_pip202314-01_1680311435pdf\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-04-01 11:10:35\"<div class=\"access-path\">$value[3]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-04-01 11:10:35\"<div class=\"access-path\">$value[3]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65a86a2e6b7601705536046\"<div class=\"access-path\">$value[4]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[4]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[4]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"34.00\"<div class=\"access-path\">$value[4]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (18) \"Was uploaded later\"<div class=\"access-path\">$value[4]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2020-11-11\"<div class=\"access-path\">$value[4]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-01_1705537301.pdf\"<div class=\"access-path\">$value[4]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (1.1KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.1KB Jan 18 2024 public/uploads/payment_files/paydocs_142023-01_1705537301.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 10:00:46\"<div class=\"access-path\">$value[4]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 10:21:54\"<div class=\"access-path\">$value[4]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"6422794e61dae1679980878\"<div class=\"access-path\">$value[5]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (10) \"eu20231401\"<div class=\"access-path\">$value[5]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[5]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (7) \"2324.45\"<div class=\"access-path\">$value[5]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (15) \"This is the cos\"<div class=\"access-path\">$value[5]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2023-03-03\"<div class=\"access-path\">$value[5]['paymentdate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>filepath</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['filepath']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-03-28 15:21:18\"<div class=\"access-path\">$value[5]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-03-28 15:21:18\"<div class=\"access-path\">$value[5]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"642282c58f4c31679983301\"<div class=\"access-path\">$value[6]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (10) \"eu20231401\"<div class=\"access-path\">$value[6]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[6]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (8) \"14345.23\"<div class=\"access-path\">$value[6]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2023-03-08\"<div class=\"access-path\">$value[6]['paymentdate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>filepath</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['filepath']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-03-28 16:01:41\"<div class=\"access-path\">$value[6]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-03-28 16:01:41\"<div class=\"access-path\">$value[6]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[7]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"6426232c70ab01680220972\"<div class=\"access-path\">$value[7]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (12) \"pip202314-01\"<div class=\"access-path\">$value[7]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[7]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"458.56\"<div class=\"access-path\">$value[7]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (16) \"Front up PAyment\"<div class=\"access-path\">$value[7]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2023-03-11\"<div class=\"access-path\">$value[7]['paymentdate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>filepath</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['filepath']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-03-31 10:02:52\"<div class=\"access-path\">$value[7]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[7]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-04-01 10:22:00\"<div class=\"access-path\">$value[7]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[7]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[8]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"64262488a17871680221320\"<div class=\"access-path\">$value[8]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (12) \"pip202314-01\"<div class=\"access-path\">$value[8]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[8]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[8]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (24) \"2nd Payment of the rules\"<div class=\"access-path\">$value[8]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2023-03-13\"<div class=\"access-path\">$value[8]['paymentdate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>filepath</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['filepath']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-03-31 10:08:40\"<div class=\"access-path\">$value[8]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[8]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-04-01 11:20:58\"<div class=\"access-path\">$value[8]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[8]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[9]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"6427982a1f0fb1680316458\"<div class=\"access-path\">$value[9]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (11) \"eu202314-01\"<div class=\"access-path\">$value[9]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[9]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"140.00\"<div class=\"access-path\">$value[9]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (11) \"1st Payment\"<div class=\"access-path\">$value[9]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2023-03-31\"<div class=\"access-path\">$value[9]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (87) \"http://localhost/promis/public/uploads/payment_files/paydocs_eu202314-01_168...<div class=\"access-path\">$value[9]['filepath']</div></dt><dd><pre>http://localhost/promis/public/uploads/payment_files/paydocs_eu202314-01_1680316458.pdf\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-04-01 12:34:18\"<div class=\"access-path\">$value[9]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[9]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-04-01 12:34:18\"<div class=\"access-path\">$value[9]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[10]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"64278526bc9f31680311590\"<div class=\"access-path\">$value[10]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (12) \"pip202314-01\"<div class=\"access-path\">$value[10]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[10]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (7) \"1232.00\"<div class=\"access-path\">$value[10]['amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (11) \"This tokin\"<div class=\"access-path\">$value[10]['description']</div></dt><dd><pre>This  tokin\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2023-04-05\"<div class=\"access-path\">$value[10]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (87) \"http://localhost/promis/public/uploads/payment_files/paydocs_pip202314-01_16...<div class=\"access-path\">$value[10]['filepath']</div></dt><dd><pre>http://localhost/promis/public/uploads/payment_files/paydocs_pip202314-01_1680311590pdf\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-04-01 11:13:10\"<div class=\"access-path\">$value[10]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[10]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2023-04-01 11:17:29\"<div class=\"access-path\">$value[10]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[10]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[11]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"64f6938c5c1dc1693881228\"<div class=\"access-path\">$value[11]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[11]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[11]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"45.00\"<div class=\"access-path\">$value[11]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (36) \"1st Payment to Wanjuwa for the posts\"<div class=\"access-path\">$value[11]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2023-07-12\"<div class=\"access-path\">$value[11]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-01_1693881477.pdf\"<div class=\"access-path\">$value[11]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (28.5KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.5KB Sep 05 2023 public/uploads/payment_files/paydocs_142023-01_1693881477.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-09-05 12:33:48\"<div class=\"access-path\">$value[11]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[11]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 10:20:52\"<div class=\"access-path\">$value[11]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[11]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>12</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[12]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[12]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"64c9d1d56931c1690948053\"<div class=\"access-path\">$value[12]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[12]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[12]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"23.23\"<div class=\"access-path\">$value[12]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (12) \"Half Payment\"<div class=\"access-path\">$value[12]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2023-08-02\"<div class=\"access-path\">$value[12]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-01_1690948053.txt\"<div class=\"access-path\">$value[12]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (581.4KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.4KB Aug 02 2023 public/uploads/payment_files/paydocs_142023-01_1690948053.txt\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-08-02 13:47:33\"<div class=\"access-path\">$value[12]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[12]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 13:57:36\"<div class=\"access-path\">$value[12]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[12]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>13</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[13]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value[13]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"654ad71c2d2881699403548\"<div class=\"access-path\">$value[13]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[13]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[13]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"46.00\"<div class=\"access-path\">$value[13]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (25) \"This is the third payment\"<div class=\"access-path\">$value[13]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2023-10-31\"<div class=\"access-path\">$value[13]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-01_1699403548.csv\"<div class=\"access-path\">$value[13]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER>  <GROUP> 08 2023 public/uploads/payment_files/paydocs_142023-01_1699403548.csv\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2023-11-08 10:32:28\"<div class=\"access-path\">$value[13]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[13]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 10:21:01\"<div class=\"access-path\">$value[13]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[13]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[13]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>14</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[14]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[14]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65a868972fbb21705535639\"<div class=\"access-path\">$value[14]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[14]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[14]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"45.00\"<div class=\"access-path\">$value[14]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (21) \"This is front Payment\"<div class=\"access-path\">$value[14]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2023-11-12\"<div class=\"access-path\">$value[14]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-01_1705535639.pdf\"<div class=\"access-path\">$value[14]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (1.1KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.1KB Jan 18 2024 public/uploads/payment_files/paydocs_142023-01_1705535639.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 09:53:59\"<div class=\"access-path\">$value[14]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[14]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 12:50:10\"<div class=\"access-path\">$value[14]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[14]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[14]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>15</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[15]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[15]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65a86e6eca1dc1705537134\"<div class=\"access-path\">$value[15]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[15]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[15]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"55.00\"<div class=\"access-path\">$value[15]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (16) \"This is new file\"<div class=\"access-path\">$value[15]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2023-12-12\"<div class=\"access-path\">$value[15]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-01_1705537181.pdf\"<div class=\"access-path\">$value[15]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (931.1KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.1KB Jan 18 2024 public/uploads/payment_files/paydocs_142023-01_1705537181.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 10:18:54\"<div class=\"access-path\">$value[15]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[15]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-21 15:38:51\"<div class=\"access-path\">$value[15]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[15]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[15]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>16</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[16]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[16]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65a9f813510641705637907\"<div class=\"access-path\">$value[16]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (10) \"202414-001\"<div class=\"access-path\">$value[16]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[16]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"30.00\"<div class=\"access-path\">$value[16]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (22) \"This is an overpayment\"<div class=\"access-path\">$value[16]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2024-01-04\"<div class=\"access-path\">$value[16]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (62) \"public/uploads/payment_files/paydocs_202414-001_1705637907.pdf\"<div class=\"access-path\">$value[16]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (1.1KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.1KB Jan 19 2024 public/uploads/payment_files/paydocs_202414-001_1705637907.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-01-19 14:18:27\"<div class=\"access-path\">$value[16]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[16]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-01-19 14:18:27\"<div class=\"access-path\">$value[16]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[16]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[16]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>17</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[17]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[17]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65a7b8c2544881705490626\"<div class=\"access-path\">$value[17]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-02\"<div class=\"access-path\">$value[17]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[17]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[17]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (15) \"This is payment\"<div class=\"access-path\">$value[17]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2024-01-09\"<div class=\"access-path\">$value[17]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-02_1705490626.pdf\"<div class=\"access-path\">$value[17]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (28.5KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.5KB Jan 17 2024 public/uploads/payment_files/paydocs_142023-02_1705490626.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-01-17 21:23:46\"<div class=\"access-path\">$value[17]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[17]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-01-17 21:49:19\"<div class=\"access-path\">$value[17]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[17]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[17]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>18</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[18]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[18]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65a8a0c1c25941705550017\"<div class=\"access-path\">$value[18]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[18]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[18]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[18]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (11) \"Total Payed\"<div class=\"access-path\">$value[18]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2024-01-09\"<div class=\"access-path\">$value[18]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-01_1705550017.pdf\"<div class=\"access-path\">$value[18]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (1.1KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.1KB Jan 18 2024 public/uploads/payment_files/paydocs_142023-01_1705550017.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 13:53:37\"<div class=\"access-path\">$value[18]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[18]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-01-18 13:53:37\"<div class=\"access-path\">$value[18]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[18]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[18]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>19</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[19]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"20\"<div class=\"access-path\">$value[19]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65cabe3a74ad51707785786\"<div class=\"access-path\">$value[19]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-07\"<div class=\"access-path\">$value[19]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[19]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (7) \"4325.20\"<div class=\"access-path\">$value[19]['amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (18) \"Up front payment \"<div class=\"access-path\">$value[19]['description']</div></dt><dd><pre>Up front payment\r\n\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2024-01-31\"<div class=\"access-path\">$value[19]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-07_1707785786.pdf\"<div class=\"access-path\">$value[19]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (28.5KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.5KB Feb 13 2024 public/uploads/payment_files/paydocs_142023-07_1707785786.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 10:56:26\"<div class=\"access-path\">$value[19]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[19]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:01:01\"<div class=\"access-path\">$value[19]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[19]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[19]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>20</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[20]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[20]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65cabf9552e171707786133\"<div class=\"access-path\">$value[20]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-07\"<div class=\"access-path\">$value[20]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[20]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"203.23\"<div class=\"access-path\">$value[20]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (12) \"Cool payment\"<div class=\"access-path\">$value[20]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2024-02-08\"<div class=\"access-path\">$value[20]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-07_1707786133.pdf\"<div class=\"access-path\">$value[20]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (29.2KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.2KB Feb 13 2024 public/uploads/payment_files/paydocs_142023-07_1707786133.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:02:13\"<div class=\"access-path\">$value[20]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[20]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:03:29\"<div class=\"access-path\">$value[20]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[20]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[20]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>21</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[21]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[21]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65cacc25872621707789349\"<div class=\"access-path\">$value[21]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-05\"<div class=\"access-path\">$value[21]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[21]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (9) \"100000.00\"<div class=\"access-path\">$value[21]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (9) \"NExt pary\"<div class=\"access-path\">$value[21]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2024-02-08\"<div class=\"access-path\">$value[21]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-05_1707789349.pdf\"<div class=\"access-path\">$value[21]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (29.2KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.2KB Feb 13 2024 public/uploads/payment_files/paydocs_142023-05_1707789349.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:55:49\"<div class=\"access-path\">$value[21]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[21]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-13 11:55:49\"<div class=\"access-path\">$value[21]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[21]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[21]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>22</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[22]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"24\"<div class=\"access-path\">$value[22]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65d58b27369f41708493607\"<div class=\"access-path\">$value[22]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[22]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[22]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"80.90\"<div class=\"access-path\">$value[22]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (18) \"This is the timing\"<div class=\"access-path\">$value[22]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2024-02-14\"<div class=\"access-path\">$value[22]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-01_1708493607.pdf\"<div class=\"access-path\">$value[22]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (1.1KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.1KB Feb 21 2024 public/uploads/payment_files/paydocs_142023-01_1708493607.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-21 15:33:27\"<div class=\"access-path\">$value[22]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[22]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-02-21 15:39:20\"<div class=\"access-path\">$value[22]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[22]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[22]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>23</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[23]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[23]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65d587484e6fa1708492616\"<div class=\"access-path\">$value[23]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[23]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[23]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"50.00\"<div class=\"access-path\">$value[23]['amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (64) \"This is another follow up payment Following the initial payment\"<div class=\"access-path\">$value[23]['description']</div></dt><dd><pre>This is another follow up payment\r\nFollowing the initial payment\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2024-02-21\"<div class=\"access-path\">$value[23]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-01_1708494135.pdf\"<div class=\"access-path\">$value[23]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (1.1KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.1KB Feb 21 2024 public/uploads/payment_files/paydocs_142023-01_1708494135.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-02-21 15:16:56\"<div class=\"access-path\">$value[23]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[23]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-03-12 15:25:04\"<div class=\"access-path\">$value[23]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[23]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[23]['status']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>24</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[24]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"25\"<div class=\"access-path\">$value[24]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (23) \"65efe76d0570c1710221165\"<div class=\"access-path\">$value[24]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>procode</dfn> =&gt; <var>string</var> (9) \"142023-01\"<div class=\"access-path\">$value[24]['procode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[24]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>amount</dfn> =&gt; <var>string</var> (8) \"20333.00\"<div class=\"access-path\">$value[24]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>description</dfn> =&gt; <var>string</var> (32) \"This is one of the payments done\"<div class=\"access-path\">$value[24]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>paymentdate</dfn> =&gt; <var>string</var> (10) \"2024-02-29\"<div class=\"access-path\">$value[24]['paymentdate']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>filepath</dfn> =&gt; <var>string</var> (61) \"public/uploads/payment_files/paydocs_142023-01_1710221165.pdf\"<div class=\"access-path\">$value[24]['filepath']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (72.4KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP>.4KB Mar 12 2024 public/uploads/payment_files/paydocs_142023-01_1710221165.pdf\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_at</dfn> =&gt; <var>string</var> (19) \"2024-03-12 15:26:05\"<div class=\"access-path\">$value[24]['create_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (7) \"Dok Man\"<div class=\"access-path\">$value[24]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_at</dfn> =&gt; <var>string</var> (19) \"2024-03-12 15:26:05\"<div class=\"access-path\">$value[24]['update_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[24]['update_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[24]['status']</div></dt></dl></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1747570499</pre>", "_ci_previous_url": "http://localhost/promis/index.php/", "username": "minad", "name": "<PERSON><PERSON>", "role": "admin", "status": "1", "orgname": "East Sepik Provincial Administration", "orglogo": "public/uploads/org_logo/2345_1707199489.png", "orgcode": "2345", "org_lock_code": "PG14", "org_lock_name": "East Sepik Province", "org_lock_level": "province", "org_cgps_lon": "143.293196", "org_cgps_lat": "-4.294692", "org_cgps_zoom": "8", "is_logged_in": "yes"}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost/promis/dashboard", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9", "Cookie": "ci_session=49gsblks3oh2t1s0l9rgg45u2s1muehn"}, "cookies": {"ci_session": "49gsblks3oh2t1s0l9rgg45u2s1muehn"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.3.2", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/promis", "timezone": "Pacific/Port_Moresby", "locale": "en", "cspEnabled": false}}