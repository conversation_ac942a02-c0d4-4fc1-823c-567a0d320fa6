<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">

            <div class="col-sm-6">
                <h1 class="m-0"><?= $pro['name'] ?></h1>
                <h5>SETTINGS</h5>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>projects"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Projects</a></li>
                    <li class="breadcrumb-item active"><?= $pro['name'] ?></li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark">
                    <a href="<?= base_url() ?>projects" class="btn btn-info btn-sm"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Projects</a></li>
                    <span class=" float-right"> <?= $pro['name'] ?> </span>
                </div>
            </div>
        </div>
        <!-- /.col -->
    </div>
    <!-- /.row -->

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-info">
                    <span> <i class="fa fa-info-circle" aria-hidden="true"></i></span>
                    Project Information
                    <a name="" id="" class="btn btn-dark btn-sm btn-sm float-right" href="<?= base_url() ?>edit_projects/<?= $pro['procode'] ?>" role="button"><i class="fa fa-pen"></i> Edit</a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-md-3">
                            <label for="my-input">Project Code</label>
                            <p><?= $pro['procode'] ?></p>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="my-input">Project Name</label>
                            <p><?= $pro['name'] ?></p>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="my-input">Project Date</label>
                            <p><?= dateforms($pro['pro_date']) ?></p>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="my-input">Project Site: </label>
                            <span><?= $pro['pro_site'] ?></span>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="my-input">Location: </label>
                            <span><?= $set_country['name'] ?>, <?= $get_provinces['name'] ?>, <?= $get_districts['name'] ?>
                                <?php
                                if (!empty($get_llgs['name'])) {
                                    echo "," . $get_llgs['name'];
                                }
                                ?>
                            </span>
                        </div>

                        <div class="form-group col-md-12">
                            <label for="my-input">Project Description</label>
                            <p><?= $pro['description'] ?></p>
                        </div>

                    </div>


                </div>
                <div class="card-footer">
                    <small><b>Update</b> <?= datetimeforms($pro['pro_update_at']) ?> / <?= $pro['pro_update_by'] ?></small>
                </div>

            </div>
            <!-- /.card -->
        </div>
        <!-- /.col -->
        <div class="col-md-4">
            <div class="row">
                <div class="col-md-12">
                    <!-- Status -->
                    <div class="card">
                        <div class="card-header bg-info">
                            <span class=""> <i class="fas fa-business-time" aria-hidden="true"></i></span>
                            Project Status
                            <a name="" id="" class="btn btn-dark btn-sm btn-sm float-right" href="<?= base_url() ?>edit_projects_status/<?= $pro['procode'] ?>" role="button"><i class="fa fa-pen"></i> Edit</a>
                        </div>
                        <div class="card-body">
                            <span for="">Status:</span>
                            <strong><?= strtoupper($pro['status']) ?></strong>
                            <p class=" text-muted"><?= $pro['statusnotes'] ?></p>
                        </div>
                        <div class="card-footer">
                            <small><b>Update: </b> <?= datetimeforms($pro['status_at']) ?> / <?= ($pro['status_by']) ?> </small>
                        </div>

                    </div>
                    <!-- /.card -->

                    <!-- ================== Budgeted -->
                    <div class="card">
                        <div class="card-header bg-info ">
                            <i class="fas fa-dollar-sign    "></i> Project Budget

                            <!-- Button trigger modal -->
                            <span class="float-right btn btn-dark btn-sm btn-sm" data-toggle="modal" data-target="#edit_project_budget">
                                <i class="fas fa-pen text-light  "></i> Edit
                            </span>

                            <!-- Modal -->
                            <div class="modal fade" id="edit_project_budget" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                <div class="modal-dialog modal-lg" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header bg-dark ">
                                            <h5 class="modal-title">Upate Budget</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <?= form_open("edit_project_budget", ['id' => 'editProjectBudgetForm']) ?>
                                        <div class="modal-body text-dark">
                                            <div class="form-group">
                                                <label for="">Budget Amount</label>
                                                <input class="form-control" type="number" step=".01" min="0" name="budget" id="" value="<?= $pro['budget'] ?>" placeholder="Amount" required>
                                                <small id="helpId" class="form-text text-muted">Enter budgeted amount</small>
                                            </div>
                                            <div class="form-group">
                                                <label for="">Funding Source</label>
                                                <select name="fund_source" class="form-control">
                                                    <?php
                                                    foreach ($select as $sel) :
                                                        if ($pro['fund'] == $sel['value']) {
                                                    ?>
                                                            <option selected value="<?= $pro['fund'] ?>"><?= $sel['item'] ?></option>
                                                        <?php
                                                        } else {
                                                        ?>
                                                            <option value="<?= $sel['value'] ?>"><?= $sel['item'] ?></option>
                                                    <?php
                                                        }
                                                    endforeach;
                                                    ?>

                                                    <option value=""></option>
                                                </select>
                                                <small id="helpId" class="form-text text-muted">Enter Funding Source</small>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <input type="hidden" name="pro_id" value="<?= $pro['id'] ?>">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                            <button type="button" class="btn btn-dark " id="btnSaveBudget">Save Budget</button>
                                        </div>
                                        <?= form_close() ?>

                                        <script>
                                            $(document).ready(function() {

                                                // Add keypress event listener to the form input fields
                                                $('#editProjectBudgetForm input').keypress(function(e) {
                                                    if (e.which == 13) {
                                                        e.preventDefault(); // Prevent the default form submission
                                                        $('#btnSaveBudget').click(); // Trigger the AJAX function
                                                    }
                                                });


                                                $('#btnSaveBudget').on('click', function() {

                                                    // Serialize form data
                                                    var formData = $('#editProjectBudgetForm').serialize();

                                                    $.ajax({
                                                        url: "<?= base_url(); ?>edit_project_budget",
                                                        type: 'POST',
                                                        data: formData,
                                                        beforeSend: function() {
                                                            // Display a loading indicator
                                                            $('#btnSaveBudget').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Uploading...');
                                                        },
                                                        success: function(response) {

                                                            // Optionally, display a success message to the user
                                                            if (response.status === 'success') {
                                                                // Display a success message to the user
                                                                toastr.success(response.message);

                                                                // Reload page after 1 second
                                                                setTimeout(function() {
                                                                    location.reload();
                                                                }, 1000);
                                                            } else {
                                                                // Display an error message to the user
                                                                toastr.error(response.message);

                                                                // Reload page after 1 second
                                                                setTimeout(function() {
                                                                    location.reload();
                                                                }, 2000);
                                                            }


                                                        },
                                                        error: function(error) {
                                                            console.log(error.responseText);
                                                        }
                                                    });

                                                });



                                            });
                                        </script>

                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="card-body">
                            <div class="">
                                <label>Budget: </label><span class=" float-right"> <?= COUNTRY_CURRENCY ?> <?= number_format($pro['budget'], 2) ?></span>
                                <br>
                                <label>Funding Source: </label><span class=" float-right"> <?= strtoupper($pro['fund']) ?></span>
                            </div>

                        </div>
                        <!-- /.card-body -->
                        <div class="card-footer">
                            <small><b>Update</b> <?= datetimeforms($pro['budget_at']) ?> / <?= $pro['budget_by'] ?> </small>
                        </div>

                    </div>
                    <!-- /.card -->

                </div>
                <!-- /.col -->

            </div>
            <!-- /.row -->
        </div>
        <!-- / .col -->

    </div>
    <!-- /.row -->

    <!-- End Project Details -->

    <div class="row">
        <!-- ================== project officer -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info ">
                    <i class="fas fa-user-tie    "></i> Project Officer
                    <a class="btn btn-dark btn-sm float-right" href="<?= base_url() ?>edit_projects_officers/<?= $pro['ucode'] ?>"><i class="fas fa-pen    "></i> Edit</a>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="">Project Officer</label>
                        <span class=" float-right"><?= $pro['pro_officer_name'] ?></span>
                    </div>
                    <div class="form-group">
                        <label for="">Work Scope</label>
                        <p><?= $pro['pro_officer_scope'] ?></p>
                    </div>

                </div>

            </div>
        </div>

        <!-- ================== contractor -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info ">
                    <i class="fa fa-wrench" aria-hidden="true"></i> Contractor
                    <a class="btn btn-dark btn-sm float-right " href="<?= base_url() ?>edit_projects_contractors/<?= $pro['procode'] ?>"><i class="fas fa-pen  text-light   "></i> Edit</a>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="">Contactor</label>
                        <span class=" float-right"><?= $pro['contractor_code'] ?> - <?= $pro['contractor_name'] ?></span>
                    </div>
                    <div class="form-group">
                        <label for="">Contact File</label>

                        <span class=" float-right">
                            <?php if (!empty($pro['contract_file'])) : ?>
                                <a href="<?= base_url() ?><?= $pro['contract_file'] ?>"><i class="fa fa-download" aria-hidden="true"></i> Download </a>
                            <?php endif; ?>
                        </span>
                    </div>
                </div>

            </div>
        </div>


    </div>



    <!-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx -->

    <div class="row p-2">

        <!-- main half split -->
        <div class="col-md-12">
            <div class="row">

                <!-- ======================= PHASES AND MILESTONES -->

                <!-- ================== phases and milestones -->
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info ">
                            <i class="fas fa-clipboard-check    "></i> Phases and Milestones

                            <a href="<?= base_url() ?>project_phases/<?= $pro['procode'] ?>" class="btn btn-dark btn-sm float-right"> <i class="fas fa-pen    "></i> Edit </a>

                        </div>
                        <div class="card-body p-0">
                            <ul class="list-group">
                                <?php foreach ($phases as $ph) : ?>
                                    <li class="list-group-item text-dark ">
                                        <div class=" float-left font-weight-bolder">
                                            <a class="" href="<?= base_url() ?>open_prophases/<?= $ph['ucode'] ?>">
                                                <strong class=" float-left align-bottom"> <i class="fa fa-angle-down" aria-hidden="true"></i> <?= $ph['phases'] ?></strong>

                                            </a>
                                        </div>

                                    </li>
                                    <li class="list-group-item p-0">

                                        <table class="table">
                                            <tbody>
                                                <?php foreach ($milestones as $ms) :
                                                    if ($ms['phase_id'] == $ph['id']) {
                                                ?>
                                                        <tr>
                                                            <td scope="row"><?= $ms['milestones'] ?> </td>
                                                            <td><?= dateforms($ms['datefrom']) ?> - <?= dateforms($ms['dateto']) ?></td>
                                                            <td><?= get_status_icon($ms['checked']) ?></td>
                                                        </tr>
                                                <?php }
                                                endforeach; ?>
                                            </tbody>
                                        </table>

                                    </li>
                                <?php endforeach; ?>
                            </ul>

                        </div>

                    </div>
                </div>






                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info ">
                            <i class="fa fa-file-pdf" aria-hidden="true"></i> Project Files
                            <!-- Button trigger modal -->
                            <button type="button" class="btn btn-sm btn-dark float-right" data-toggle="modal" data-target="#prodocs">
                                <i class="fa fa-plus-circle" aria-hidden="true"></i> Add Project Files
                            </button>

                            <!-- Modal -->
                            <div class="modal fade" id="prodocs" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                <div class="modal-dialog modal-lg" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header bg-dark">
                                            <h5 class="modal-title"> <i class="fas fa-upload    "></i> Upload Project Files</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>

                                        <div class="modal-body text-dark">

                                            <?= form_open_multipart('prodocs_upload', ['id' => 'projectFileForm']) ?>
                                            <div class="form-group">
                                                <label for="exampleInputFile">File Title</label>
                                                <div class="input-group">
                                                    <input type="text" name="name" placeholder="File Title" class=" form-control" required>
                                                </div>

                                                <label for="exampleInputFile">Upload Project Files</label>
                                                <div class="input-group">
                                                    <div class="custom-file">
                                                        <input type="file" class="custom-file-input" name="prodocs" id="exampleInputFile" required>
                                                        <label class="custom-file-label" for="exampleInputFile">Choose Files
                                                        </label>
                                                    </div>
                                                </div>

                                            </div>
                                            <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                                            <input type="hidden" name="proid" value="<?= $pro['id'] ?>">
                                            <button type="button" class="btn btn-dark float-right" id="btnUploadFiles">
                                                <i class="fa fa-save" aria-hidden="true"></i> Save
                                            </button>
                                            <?= form_close() ?>

                                            <script>
                                                $(document).ready(function() {

                                                    // Add keypress event listener to the form input fields
                                                    $('#projectFileForm input').keypress(function(e) {
                                                        if (e.which == 13) {
                                                            e.preventDefault(); // Prevent the default form submission
                                                            $('#btnUploadFiles').click(); // Trigger the AJAX function
                                                        }
                                                    });


                                                    $('#btnUploadFiles').on('click', function() {
                                                        // Create FormData object to store form data and files
                                                        var formData = new FormData($('#projectFileForm')[0]);

                                                        // Send an AJAX request
                                                        $.ajax({
                                                            url: "<?= base_url('prodocs_upload'); ?>", // Update this with your controller method
                                                            type: 'POST',
                                                            data: formData,
                                                            contentType: false,
                                                            processData: false,
                                                            beforeSend: function() {
                                                                // Display a loading indicator
                                                                $('#btnUploadFiles').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Uploading...');
                                                            },
                                                            success: function(response) {
                                                                // Handle the success response
                                                                console.log(response);

                                                                // Optionally, display a success message to the user
                                                                if (response.status === 'success') {
                                                                    // Display a success message to the user
                                                                    toastr.success(response.message);

                                                                    // Reload page after 1 second
                                                                    setTimeout(function() {
                                                                        location.reload();
                                                                    }, 1000);
                                                                } else {
                                                                    // Display an error message to the user
                                                                    toastr.error(response.message);

                                                                    // Reload page after 1 second
                                                                    setTimeout(function() {
                                                                        location.reload();
                                                                    }, 2000);
                                                                }

                                                            },
                                                            error: function(error) {
                                                                // Handle the error response
                                                                console.log(error.responseText);

                                                                // Optionally, display an error message to the user
                                                                toastr.error('Error uploading files.');
                                                            }
                                                        });
                                                    });
                                                });
                                            </script>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- ./modal -->
                        </div>
                        <div class="card-body p-0">
                            <ul class="list-group">
                                <?php foreach ($prodocs as $pd) : ?>
                                    <li class="list-group-item">
                                        <?= $pd['name'] ?>(.<?= getfileExtension($pd['filepath']) ?>)
                                        <a href="<?= base_url() ?><?= $pd['filepath'] ?>" class="btn btn-default btn-sm text-bold">
                                            <i class="fa fa-download" aria-hidden="true"></i> Download
                                        </a>

                                        <div class="input-group-prepend float-right">
                                            <button type="button" class="btn btn-dark btn-sm dropdown-toggle" data-toggle="dropdown">
                                                Action
                                            </button>
                                            <div class="dropdown-menu">
                                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#editprodocs<?= $pd['id'] ?>">Edit</a>
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item text-danger" href="#" data-toggle="modal" data-target="#delprodocs<?= $pd['id'] ?>">Delete</a>
                                            </div>
                                        </div>
                                    </li>

                                    <!-- Modal -->
                                    <div class="modal fade" id="editprodocs<?= $pd['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                        <div class="modal-dialog modal-lg" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header bg-dark">
                                                    <h5 class="modal-title"> <i class="fas fa-edit    "></i> Project Files</h5>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>

                                                <div class="modal-body text-dark">

                                                    <?= form_open_multipart('prodocs_edit', ['id' => 'editProDocsForm' . $pd['id']]) ?>

                                                    <div class="form-group">
                                                        <label for="exampleInputFile">File Title</label>
                                                        <div class="input-group">
                                                            <input type="text" name="name" placeholder="File Title" class=" form-control" value="<?= $pd['name'] ?>" required>
                                                        </div>

                                                        <label for="exampleInputFile">Project Files</label>
                                                        <div class="input-group">
                                                            <div class="custom-file">
                                                                <input type="file" class="custom-file-input" name="prodocs" id="exampleInputFile">
                                                                <label class="custom-file-label" for="exampleInputFile">Choose Files
                                                                </label>
                                                            </div>
                                                        </div>

                                                    </div>
                                                    <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                                                    <input type="hidden" name="pdid" value="<?= $pd['id'] ?>">
                                                    <button type="button" class="btn btn-dark float-right" id="btnEditProdocs<?= $pd['id'] ?>">
                                                        <i class="fa fa-save" aria-hidden="true"></i> Save
                                                    </button>
                                                    <?= form_close() ?>


                                                    <script>
                                                        $(document).ready(function() {


                                                            // Add keypress event listener to the form input fields
                                                            $('#editProDocsForm<?= $pd['id'] ?> input').keypress(function(e) {
                                                                if (e.which == 13) {
                                                                    e.preventDefault(); // Prevent the default form submission
                                                                    $('#btnEditProdocs<?= $pd['id'] ?>').click(); // Trigger the AJAX function
                                                                }
                                                            });


                                                            $('#btnEditProdocs<?= $pd['id'] ?>').on('click', function() {
                                                                // Create FormData object to store form data and files
                                                                var formData = new FormData($('#editProDocsForm<?= $pd['id'] ?>')[0]);

                                                                // Send an AJAX request
                                                                $.ajax({
                                                                    url: "<?= base_url('prodocs_edit'); ?>", // Update this with your controller method
                                                                    type: 'POST',
                                                                    data: formData,
                                                                    contentType: false,
                                                                    processData: false,
                                                                    beforeSend: function() {
                                                                        // Display a loading indicator
                                                                        $('#btnEditProdocs<?= $pd['id'] ?>').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Uploading...');
                                                                    },
                                                                    success: function(response) {
                                                                        // Handle the success response
                                                                        console.log(response);

                                                                        // Optionally, display a success message to the user
                                                                        toastr.success(response.message);

                                                                        // Reload page after 1 seconds
                                                                        setTimeout(function() {
                                                                            location.reload();
                                                                        }, 1000);
                                                                    },
                                                                    error: function(error) {
                                                                        // Handle the error response
                                                                        console.log(error.responseText);

                                                                        // Optionally, display an error message to the user
                                                                        toastr.error(response.message);
                                                                    }
                                                                });
                                                            });
                                                        });
                                                    </script>


                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- ./modal -->

                                    <!-- Modal -->
                                    <div class="modal fade" id="delprodocs<?= $pd['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                        <div class="modal-dialog modal-lg" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header bg-danger">
                                                    <h5 class="modal-title"> <i class="fas fa-exclamation-triangle    "></i> You're about to Delete! </h5>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>

                                                <div class="modal-body text-dark">

                                                    <?= form_open_multipart('prodocs_delete', ['id' => 'delProDocsForm' . $pd['id']]) ?>

                                                    <div class="form-group">
                                                        <label for="exampleInputFile">File Title</label>
                                                        <div class="input-group">
                                                            <?= $pd['name'] ?>(.<?= getfileExtension($pd['filepath']) ?>)
                                                        </div>

                                                    </div>
                                                    <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                                                    <input type="hidden" name="pdid" value="<?= $pd['id'] ?>">
                                                    <input type="hidden" name="pdname" value="<?= $pd['name'] ?>">
                                                    <button type="button" class="btn btn-danger float-right" id="btnDelProdocs<?= $pd['id'] ?>">
                                                        <i class="fa fa-trash-alt" aria-hidden="true"></i> Delete
                                                    </button>
                                                    <?= form_close() ?>


                                                    <script>
                                                        $(document).ready(function() {

                                                            // Add keypress event listener to the form input fields
                                                            $('#delProDocsForm<?= $pd['id'] ?> input').keypress(function(e) {
                                                                if (e.which == 13) {
                                                                    e.preventDefault(); // Prevent the default form submission
                                                                    $('#btnDelProdocs<?= $pd['id'] ?>').click(); // Trigger the AJAX function
                                                                }
                                                            });


                                                            $('#btnDelProdocs<?= $pd['id'] ?>').on('click', function() {
                                                                // Create FormData object to store form data and files
                                                                var formData = new FormData($('#delProDocsForm<?= $pd['id'] ?>')[0]);

                                                                // Send an AJAX request
                                                                $.ajax({
                                                                    url: "<?= base_url('prodocs_delete'); ?>", // Update this with your controller method
                                                                    type: 'POST',
                                                                    data: formData,
                                                                    contentType: false,
                                                                    processData: false,
                                                                    beforeSend: function() {
                                                                        // Display a loading indicator
                                                                        $('#btnDelProdocs<?= $pd['id'] ?>').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Deleting...');
                                                                    },
                                                                    success: function(response) {
                                                                        // Handle the success response
                                                                        console.log(response);

                                                                        // Optionally, display a success message to the user
                                                                        if (response.status === 'success') {
                                                                            // Display a success message to the user
                                                                            toastr.success(response.message);

                                                                            // Reload page after 1 second
                                                                            setTimeout(function() {
                                                                                location.reload();
                                                                            }, 1000);
                                                                        } else {
                                                                            // Display an error message to the user
                                                                            toastr.error(response.message);

                                                                            // Reload page after 1 second
                                                                            setTimeout(function() {
                                                                                location.reload();
                                                                            }, 2000);
                                                                        }

                                                                    },
                                                                    error: function(error) {
                                                                        // Handle the error response
                                                                        console.log(error.responseText);

                                                                        // Optionally, display an error message to the user
                                                                        toastr.error(response.message);
                                                                    }
                                                                });
                                                            });
                                                        });
                                                    </script>


                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- ./modal -->

                                <?php endforeach; ?>
                            </ul>
                        </div>

                    </div>
                </div>
                <!-- ./ col -->


                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info">
                            <i class="fas fa-money-check-alt "></i> Project Payments

                            <!-- Button trigger modal -->
                            <button type="button" class="btn btn-sm btn-dark float-right" data-toggle="modal" data-target="#addfund">
                                <i class="fas fa-plus-circle"></i> Add Payments
                            </button>

                            <!-- Modal -->
                            <div class="modal fade" id="addfund" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                <div class="modal-dialog modal-lg " role="document">
                                    <div class="modal-content">
                                        <div class="modal-header bg-dark">
                                            <h5 class="modal-title"> <i class="fas fa-dollar-sign    "></i> Add Payment</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <?= form_open_multipart('addpayments', ['id' => 'addpaymentsForm']) ?>
                                        <div class="modal-body text-dark">
                                            <div class="">
                                                <div class="form-group">
                                                    <label for="inputName" class="">Amount</label>
                                                    <input type="number" step=".01" class="form-control" name="amount" id="inputName" placeholder="0000.00" required>
                                                </div>

                                                <div class="form-group">
                                                    <label for="inputName" class="">Payment Date</label>
                                                    <input type="date" class="form-control" name="paymentdate" id="inputName" placeholder="Date" required>
                                                </div>

                                                <div class="form-group">
                                                    <textarea id="my-textarea" class="form-control" name="description" placeholder="Enter Description" rows="3" required></textarea>
                                                </div>
                                                <div class="form-group">
                                                    <label for="exampleInputFile">Upload Payment Files</label>
                                                    <div class="input-group">
                                                        <div class="custom-file">
                                                            <input type="file" class="custom-file-input" name="file_payment" id="file_payment" required accept=".pdf">
                                                            <label class="custom-file-label" for="exampleInputFile">Choose
                                                                file...</label>
                                                        </div>
                                                    </div>
                                                    <small class=" text-muted">
                                                        Upload the files for this payment
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                            <button type="button" class="btn btn-dark " id="btnAddPayments"> <i class="fa fa-paper-plane" aria-hidden="true"></i> Post Payment</button>
                                        </div>
                                        <?= form_close() ?>

                                        <script>
                                            $(document).ready(function() {

                                                // Add keypress event listener to the form input fields
                                                $('#addpaymentsForm input').keypress(function(e) {
                                                    if (e.which == 13) {
                                                        e.preventDefault(); // Prevent the default form submission
                                                        $('#btnAddPayments').click(); // Trigger the AJAX function
                                                    }
                                                });



                                                $('#btnAddPayments').on('click', function() {

                                                    // Check if a file is selected
                                                    var fileInput = $('#file_payment');
                                                    if (fileInput.get(0).files.length === 0) {
                                                        // Display an error message for file selection
                                                        toastr.error('Please select a file.');
                                                        return;
                                                    }

                                                    // Check if the selected file has the correct extension (.pdf)
                                                    var allowedExtensions = /(\.pdf)$/i;
                                                    if (!allowedExtensions.exec(fileInput.val())) {
                                                        // Display an error message for file type
                                                        toastr.error('Please select a PDF file.');
                                                        return;
                                                    }


                                                    // Create FormData object to store form data and files
                                                    var formData = new FormData($('#addpaymentsForm')[0]);

                                                    // Send an AJAX request
                                                    $.ajax({
                                                        url: "<?= base_url('addpayments'); ?>", // Update this with your controller method
                                                        type: 'POST',
                                                        data: formData,
                                                        contentType: false,
                                                        processData: false,
                                                        beforeSend: function() {
                                                            // Display a loading indicator
                                                            $('#btnAddPayments').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Uploading...');
                                                        },
                                                        success: function(response) {
                                                            // Handle the success response
                                                            console.log(response);
                                                            // Optionally, display a success message to the user
                                                            if (response.status === 'success') {
                                                                // Display a success message to the user
                                                                toastr.success(response.message);

                                                                // Reload page after 1 second
                                                                setTimeout(function() {
                                                                    location.reload();
                                                                }, 1000);
                                                            } else {
                                                                // Display an error message to the user
                                                                toastr.error(response.message);

                                                                // Reload page after 1 second
                                                                setTimeout(function() {
                                                                    location.reload();
                                                                }, 2000);
                                                            }

                                                        },
                                                        error: function(error) {
                                                            // Handle the error response
                                                            console.log(error.responseText);

                                                            // Optionally, display an error message to the user
                                                            toastr.error(response.message);
                                                        }
                                                    });
                                                });
                                            });
                                        </script>


                                    </div>
                                </div>
                            </div>
                            <!-- ./ modal -->

                        </div>
                        <div class="card-body p-0">

                            <div class="row">
                                <div class="col-md-12">
                                    <table class="table table-light table-hover">
                                        <thead class="">
                                            <tr>
                                                <th>#</th>
                                                <th>Amount(<?= COUNTRY_CURRENCY ?>)</th>
                                                <th>P.Date</th>
                                                <th>Notes</th>
                                                <th>File</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $total = array();
                                            $x = 1;
                                            foreach ($fund as $fd) : ?>
                                                <tr>
                                                    <td><?= $x++ ?></td>
                                                    <td data-toggle="tooltip" data-placement="top" title="<?= $fd['description'] ?>"><?= $total[] = number_format($fd['amount'], 2) ?></td>
                                                    <td><?= dateforms($fd['paymentdate']) ?></td>
                                                    <td><?= ($fd['description']) ?></td>
                                                    <td>
                                                        <?php if ($fd['filepath'] != "") : ?>
                                                            <a class=" btn btn-flat text-dark" href="<?= filecheck($fd['filepath']) ?>"><i class="fa fa-download" aria-hidden="true"></i></a>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td> <span class="btn btn-dark btn-sm float-right" data-toggle="modal" data-target="#editfund<?= $fd['id'] ?>"> <i class="fas fa-pen    "></i> Edit</span></td>
                                                    <!-- Modal -->
                                                    <div class="modal fade" id="editfund<?= $fd['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                                        <div class="modal-dialog modal-lg" role="document">
                                                            <div class="modal-content">
                                                                <div class="modal-header bg-dark">
                                                                    <h5 class="modal-title"> <i class="fas fa-edit"></i> Edit Payment</h5>
                                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                        <span aria-hidden="true">&times;</span>
                                                                    </button>
                                                                </div>
                                                                <?= form_open_multipart('editpayments', ['id' => 'editpaymentsForm' . $fd['id']]) ?>
                                                                <div class="modal-body">

                                                                    <div class="form-group ">
                                                                        <label for="inputName" class="col-sm-1-12 col-form-label">Amount</label>
                                                                        <input type="number" step=".01" class="form-control" name="amount" id="inputName" placeholder="0000.00" required value="<?= $fd['amount'] ?>">
                                                                    </div>

                                                                    <div class="form-group ">
                                                                        <label for="inputName" class="col-sm-1-12 col-form-label">Payment Date</label>
                                                                        <input type="date" class="form-control" name="paymentdate" id="inputName" placeholder="Date" required value="<?= $fd['paymentdate'] ?>">
                                                                    </div>

                                                                    <div class="form-group ">
                                                                        <textarea id="my-textarea" class="form-control" name="description" placeholder="Enter Description" rows="3" required><?= $fd['description'] ?></textarea>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label for="exampleInputFile">Upload Payment Files</label>
                                                                        <div class="input-group">
                                                                            <div class="custom-file">
                                                                                <input type="file" class="custom-file-input" name="file_payment" id="exampleInputFile">
                                                                                <label class="custom-file-label" for="exampleInputFile">Choose
                                                                                    file...</label>
                                                                            </div>
                                                                        </div>
                                                                        <small class=" text-muted">
                                                                            Upload the files for this payment
                                                                        </small>
                                                                    </div>

                                                                </div>
                                                                <div class="modal-footer d-flex justify-content-between ">
                                                                    <span class=" float-left">
                                                                        <small class=" float-left"><b>Create:</b> <?= datetimeforms($fd['create_at']) ?> | <?= $fd['create_by'] ?></small><br>
                                                                        <small><b>Update:</b> <?= datetimeforms($fd['update_at']) ?> | <?= $fd['update_by'] ?></small>
                                                                    </span>
                                                                    <div>
                                                                        <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                                                                        <input type="hidden" name="payid" value="<?= $fd['id'] ?>">
                                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                                        <button type="button" class="btn btn-dark" id="btnsEditPayments<?= $fd['id'] ?>">Update Payment</button>
                                                                    </div>

                                                                </div>
                                                                <?= form_close() ?>

                                                                <script>
                                                                    $(document).ready(function() {

                                                                        // Add keypress event listener to the form input fields
                                                                        $('#editpaymentsForm<?= $fd['id'] ?> input').keypress(function(e) {
                                                                            if (e.which == 13) {
                                                                                e.preventDefault(); // Prevent the default form submission
                                                                                $('#btnsEditPayments<?= $fd['id'] ?>').click(); // Trigger the AJAX function
                                                                            }
                                                                        });


                                                                        $('#btnsEditPayments<?= $fd['id'] ?>').on('click', function() {
                                                                            // Create FormData object to store form data and files
                                                                            var formData = new FormData($('#editpaymentsForm<?= $fd['id'] ?>')[0]);

                                                                            // Send an AJAX request
                                                                            $.ajax({
                                                                                url: "<?= base_url('editpayments'); ?>", // Update this with your controller method
                                                                                type: 'POST',
                                                                                data: formData,
                                                                                contentType: false,
                                                                                processData: false,
                                                                                beforeSend: function() {
                                                                                    // Display a loading indicator
                                                                                    $('#btnsEditPayments<?= $fd['id'] ?>').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Processing...');
                                                                                },
                                                                                success: function(response) {
                                                                                    // Handle the success response
                                                                                    console.log(response);

                                                                                    // Optionally, display a success message to the user
                                                                                    if (response.status === 'success') {
                                                                                        // Display a success message to the user
                                                                                        toastr.success(response.message);

                                                                                        // Reload page after 1 second
                                                                                        setTimeout(function() {
                                                                                            location.reload();
                                                                                        }, 3000);
                                                                                    } else {
                                                                                        // Display an error message to the user
                                                                                        toastr.error(response.message);

                                                                                        // Reload page after 1 second
                                                                                        setTimeout(function() {
                                                                                            location.reload();
                                                                                        }, 3000);
                                                                                    }

                                                                                },
                                                                                error: function(error) {
                                                                                    // Handle the error response
                                                                                    console.log(error.responseText);

                                                                                    // Optionally, display an error message to the user
                                                                                    toastr.error(response.message);
                                                                                }
                                                                            });
                                                                        });
                                                                    });
                                                                </script>


                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- ./ modal -->
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>

                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <b>Totals</b>
                            <address>
                                <?php $paid = array();
                                $x = 1;
                                foreach ($fund as $fd) : ?>
                                    <?php (($paid[] = $fd['amount'])) ?>
                                <?php endforeach; ?>
                                Budgeted: <span class=" float-right"><?= COUNTRY_CURRENCY ?> <?= number_format($pro['budget'], 2) ?></span> <br>
                                Paid: <span class=" float-right"><?= COUNTRY_CURRENCY ?> <?= number_format($yetto = array_sum($paid), 2) ?></span> <br>

                                <b> Outstanding: <span class=" float-right"><?= COUNTRY_CURRENCY ?> <?= number_format(($pro['budget'] - $yetto), 2) ?></span></b>

                            </address>
                        </div>
                    </div>
                </div>
                <!-- ./col -->


                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info ">
                            <span class=" float-left"> <i class="fa fa-map" aria-hidden="true"></i> Map (<?= $pro['gps'] ?>)</span>

                            <!-- Button trigger modal -->
                            <button type="button" class="btn btn-sm btn-dark float-right" data-toggle="modal" data-target="#setgps">
                                <i class=" fa fa-map-marker-alt"></i> Set Corrdinates
                            </button>

                            <!-- Modal -->
                            <div class="modal fade" id="setgps" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                <div class="modal-dialog modal-lg" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header  bg-dark">
                                            <h5 class="modal-title"> <i class="fas fa-map-marked-alt    "></i> GPS Coordinates</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>

                                        <div class="modal-body text-dark">

                                            <?= form_open_multipart('gps_set', ['id' => 'gps_setForm']) ?>

                                            <div class="form-group">
                                                <label for="exampleInputFile">Latitude</label>
                                                <input type="text" class=" form-control" name="lat" placeholder="-3.3455" value="<?= $pro['lat'] ?>">

                                            </div>
                                            <div class="form-group">
                                                <label for="exampleInputFile">Longitude</label>
                                                <input type="text" class=" form-control" name="lon" placeholder="123.3455" value="<?= $pro['lon'] ?>">

                                            </div>


                                            <div class="form-group">
                                                <label for="exampleInputFile">Upload KML Files</label>
                                                <div class="input-group">
                                                    <div class="custom-file">
                                                        <input type="file" class="custom-file-input" name="file_basekml" id="exampleInputFile" accept=".kml">
                                                        <label class="custom-file-label" for="exampleInputFile">Choose .kml
                                                            file...</label>
                                                    </div>
                                                </div>
                                                <small class=" text-muted">
                                                    Upload .KML if available
                                                </small>
                                            </div>

                                            <input type="hidden" name="proucode" value="<?= $pro['ucode'] ?>">
                                            <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                                            <input type="hidden" name="proid" value="<?= $pro['id'] ?>">
                                            <button type="button" class="btn btn-dark float-right" id="btn_savegps">
                                                <i class="fa fa-save" aria-hidden="true"></i> Save
                                            </button>
                                            <?= form_close() ?>

                                            <script>
                                                $(document).ready(function() {

                                                    // Add keypress event listener to the form input fields
                                                    $('#gps_setForm input').keypress(function(e) {
                                                        if (e.which == 13) {
                                                            e.preventDefault(); // Prevent the default form submission
                                                            $('#btn_savegps').click(); // Trigger the AJAX function
                                                        }
                                                    });


                                                    $('#btn_savegps').on('click', function() {
                                                        // Create FormData object to store form data and files
                                                        var formData = new FormData($('#gps_setForm')[0]);

                                                        // Send an AJAX request
                                                        $.ajax({
                                                            url: "<?= base_url('gps_set'); ?>", // Update this with your controller method
                                                            type: 'POST',
                                                            data: formData,
                                                            contentType: false,
                                                            processData: false,
                                                            beforeSend: function() {
                                                                // Display a loading indicator
                                                                $('#btn_savegps').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Uploading...');
                                                            },
                                                            success: function(response) {
                                                                // Handle the success response
                                                                console.log(response);

                                                                // Optionally, display a success message to the user
                                                                if (response.status === 'success') {
                                                                    // Display a success message to the user
                                                                    toastr.success(response.message);

                                                                    // Reload page after 1 second
                                                                    setTimeout(function() {
                                                                        location.reload();
                                                                    }, 1000);
                                                                } else {
                                                                    // Display an error message to the user
                                                                    toastr.error(response.message);

                                                                    // Reload page after 1 second
                                                                    setTimeout(function() {
                                                                        location.reload();
                                                                    }, 2000);
                                                                }

                                                            },
                                                            error: function(error) {
                                                                // Handle the error response
                                                                console.log(error.responseText);

                                                                // Optionally, display an error message to the user
                                                                toastr.error(response.message);
                                                            }
                                                        });
                                                    });
                                                });
                                            </script>



                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- ./modal -->

                            <!-- =============================================================================== -->

                        </div>
                        <div class="card-body">

                            <!-- 
                    extracting kml file into map
                 -->
                            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
                            <script src="https://cdnjs.cloudflare.com/ajax/libs/openlayers/4.6.5/ol.js"></script>
                            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/openlayers/4.6.5/ol.css" type="text/css">

                            <div id="map"></div>

                            <!-- Add this line -->
                            <script>
                                var vectorSources = [
                                    new ol.source.Vector({
                                        url: "<?= base_url() . $pro['kmlfile'] ?>",
                                        format: new ol.format.KML({
                                            extractStyles: false,
                                            extractAttributes: true,
                                        }),
                                        strategy: ol.loadingstrategy.bbox, // Only load visible features
                                    }),
                                    // add more VectorSource objects as needed
                                ];
                                var vectorLayers = [];
                                for (var i = 0; i < vectorSources.length; i++) {
                                    var layer = new ol.layer.Vector({
                                        source: vectorSources[i],
                                        style: function(feature) {
                                            // Only display tracks
                                            if (feature.getGeometry().getType() === "LineString") {
                                                return new ol.style.Style({
                                                    stroke: new ol.style.Stroke({
                                                        color: "red",
                                                        width: 2,
                                                    }),
                                                });
                                            }
                                        },
                                    });
                                    vectorLayers.push(layer);
                                }
                                var marker1 = new ol.Feature({
                                    geometry: new ol.geom.Point(
                                        ol.proj.fromLonLat([<?= $pro['lon'] ?>, <?= $pro['lat'] ?>])
                                    ),
                                    name: "Marker 1",
                                    description: "This is the first marker",
                                    url: "https://govhrm.wanspeen.com",
                                    link: "View",
                                });

                                var vectorPoints = new ol.source.Vector({
                                    features: [marker1],
                                });
                                var vectorPointsLayer = new ol.layer.Vector({
                                    source: vectorPoints,
                                    style: new ol.style.Style({
                                        image: new ol.style.Icon({
                                            src: "<?= base_url() ?>public/assets/system_img/marker-map.png",
                                            // size:[20,20]
                                        }),
                                    }),
                                });
                                var map = new ol.Map({
                                    layers: [
                                        new ol.layer.Tile({
                                            source: new ol.source.OSM(),
                                        }),
                                        // add all VectorLayer objects to the layers array
                                        ...vectorLayers,
                                        vectorPointsLayer,
                                    ],
                                    target: "map",
                                    view: new ol.View({
                                        center: ol.proj.fromLonLat([<?= $pro['lon'] ?>, <?= $pro['lat'] ?>]),
                                        zoom: 12,
                                    }),
                                });
                                var element = document.getElementById("popup");
                                var popup = new ol.Overlay({
                                    element: element,
                                    positioning: "bottom-center",
                                    stopEvent: false,
                                    offset: [0, -20],
                                });
                            </script>

                        </div>
                        <div class="card-footer p-2">
                            <div class="row">
                                <?php foreach ($kmlfiles as $kml) : ?>

                                    <div class="col-md-2">
                                        <a href="<?= base_url() ?><?= $kml['filepath'] ?>" class="btn btn-default btn-sm"> <i class="fa fa-download" aria-hidden="true"></i> (.kml)<br> <small><?= datetimeforms($kml['create_at']) ?> / <?= $kml['create_by'] ?></small> </a>
                                    </div>

                                <?php endforeach; ?>
                            </div>



                            <small class=" float-left"><b>Update:</b> <?= datetimeforms($pro['gps_at']) ?></small>
                            <small class=" float-right"><b>By:</b> <?= ($pro['gps_by']) ?></small>
                        </div>
                    </div>
                </div>
                <!-- ./ col -->

                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info">
                            Events
                        </div>
                        <div class="card-body">
                            <div class="tab-pane" id="timeline">
                                <!-- The timeline -->
                                <div class="timeline timeline-inverse">

                                    <?php foreach ($events as $ev) : ?>
                                        <!-- timeline time label -->
                                        <div class="time-label">
                                            <span class="bg-secondary">
                                                <?= dateforms($ev['eventdate']) ?>
                                            </span>
                                        </div>
                                        <!-- /.timeline-label -->
                                        <!-- timeline item -->
                                        <div>
                                            <i class="fas fa-calendar-check bg-info"></i>

                                            <div class="timeline-item">
                                                <span class="time"><i class="far fa-clock"></i> <?= datetimeforms($ev['create_at']) ?> </span>

                                                <h3 class="timeline-header"><a href="#"><?= $ev['create_by'] ?></a> posted</h3>

                                                <div class="timeline-body">
                                                    <p><?= $ev['event'] ?></p>
                                                    <div class="row">
                                                        <?php $x = 0;
                                                        foreach ($eventfiles as $ef) :
                                                            if ($ef['event_id'] == $ev['id']) :
                                                        ?>

                                                                <?php
                                                                // Get the file extension
                                                                $file_ext = pathinfo($ef['filepath'], PATHINFO_EXTENSION);

                                                                // Check if the file is an image
                                                                if (!in_array(strtolower($file_ext), array('jpg', 'jpeg', 'png', 'gif'))) {


                                                                    // Display the image in an image tag
                                                                ?>

                                                                    <div class="">
                                                                        <a class=" btn btn-app" href="<?= base_url() ?><?= $ef['filepath'] ?>"> <i class="fa fa-download" aria-hidden="true"></i>
                                                                            <?= $ef['id'] . "(." . $file_ext . ")" ?>
                                                                        </a>
                                                                    </div>

                                                        <?php
                                                                }

                                                            endif;
                                                        endforeach; ?>
                                                    </div>
                                                    <div class="row">
                                                        <?php $x = 0;
                                                        foreach ($eventfiles as $ef) :
                                                            if ($ef['event_id'] == $ev['id']) :
                                                        ?>

                                                                <?php
                                                                // Get the file extension
                                                                $file_ext = pathinfo($ef['filepath'], PATHINFO_EXTENSION);

                                                                // Check if the file is an image
                                                                if (in_array(strtolower($file_ext), array('jpg', 'jpeg', 'png', 'gif'))) {
                                                                    // Display the image in an image tag
                                                                ?>

                                                                    <img class="img img-fluid img-bordered" src="<?= base_url() ?><?= $ef['filepath'] ?>" width="25%" alt="ev-pic">

                                                        <?php

                                                                }
                                                            endif;
                                                        endforeach; ?>
                                                    </div>
                                                </div>
                                                <!-- ./ timeline body -->
                                                <div class="timeline-footer d-flex justify-content-between">
                                                    <span class=" float-left"><small>
                                                            <b>Updated: </b><?= datetimeforms($ev['update_at']) ?> | <?= $ev['update_by'] ?>
                                                        </small></span>

                                                </div>
                                            </div>
                                        </div>
                                        <!-- END timeline item -->

                                    <?php endforeach; ?>


                                    <div>
                                        <i class="far fa-clock bg-gray"></i>
                                    </div>
                                </div>
                            </div>
                            <!-- /.tab-pane -->
                        </div>
                        <div class="card-footer">
                            <a href="<?= base_url() ?>public/uploads/audit_files/<?= $pro['procode'] ?>.txt" class=" float-right"> <i class="fas fa-pencil-square "></i> Audit Trail</a>
                        </div>

                    </div>
                </div>
                <!-- ./col events -->


            </div>
            <!-- /. first half row -->
        </div>
        <!-- ./ 1st main half split col -->



    </div>
</section>


</body>


<script>
    $(document).ready(function() {
        $('#province').change(function() {
            var province_code = $(this).val();

            $.ajax({
                url: '<?= base_url() ?>getaddress',
                type: 'post',
                data: {
                    province_code: province_code
                },
                dataType: 'json',
                success: function(response) {
                    var len = response.district.length;

                    $("#district").empty();
                    $("#district").append("<option value=''>Select a District</option>");

                    for (var i = 0; i < len; i++) {
                        var code = response.district[i]['districtcode'];
                        var name = response.district[i]['name'];
                        //var code = response.subcategories[i]['code'];

                        $("#district").append("<option value='" + code + "'>" + name +
                            "</option>");

                    }
                }
            });
        });
    });
</script>



<?= $this->endSection() ?>