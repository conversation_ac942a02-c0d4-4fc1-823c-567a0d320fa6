<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<!-- links -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@2.9.3/dist/Chart.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.3/html2pdf.bundle.min.js"></script>


<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Project Officers Dashboad</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Go Back</a></li>
                    <li class="breadcrumb-item active"> Officers Dashboard</li>
                    <li class="breadcrumb-item active"> <button onclick="printCard('printpdf')" class=" btn btn-default btn-sm float-right d-print-block"> <i class="fa fa-print" aria-hidden="true"></i> </button></li>
                    <li class="breadcrumb-item active"> <button onclick="copyToImageHD('printpdf')" class=" btn btn-default btn-sm float-right d-print-block"> <i class="fa fa-copy" aria-hidden="true"></i> </button></li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<div class="container-fluid mt-1" id="printpdf">

    <div class="row">
        <div class="col-md-12">
            <div class="card bg-dark ">
                <!--tips: add .text-center,.text-right to the .card to change card text alignment-->
                <div class="card-header p-1">
                    <a href="<?= base_url() ?>dashboard" class=" btn btn-light text-dark"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Go Back</a>
                    <span class=" float-right btn btn-dark">Project Officers Report Dashboard</span>
                </div>

            </div>
            <!-- /.card -->
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <h5 class="float-left">Summary Report</h5>
            <div class="float-right"><?= session('orgname') ?></div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info">
                    <i class="fas fa-user-tag "></i> Project Officers Report
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>P.Officer</th>
                                <th>#. Projects</th>
                                <th>T.Budget (<?= COUNTRY_CURRENCY ?>) </th>
                                <th>T.Payments (<?= COUNTRY_CURRENCY ?>) </th>
                                <th>Status </th>
                                <th> </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $x = 1;
                            foreach ($pofficers as $off) : ?>

                                <?php
                                $projects_payments = $projects_amount = $projects_count = 0;
                                $projects_active = $projects_hold = $projects_completed = 0;


                                foreach ($projects as $pro) : ?>
                                    <?php if ($off['id'] == $pro['pro_officer_id']) :
                                        $projects_count++;
                                        $projects_amount += $pro['budget'];
                                        if ($pro['payment_total'] != null) {
                                            $projects_payments += $pro['payment_total'];
                                        }
                                        if ($pro['status'] == 'active') {
                                            $projects_active++;
                                        }
                                        if ($pro['status'] == 'hold') {
                                            $projects_hold++;
                                        }
                                        if ($pro['status'] == 'completed') {
                                            $projects_completed++;
                                        }

                                    ?>

                                    <?php endif ?>
                                <?php endforeach; ?>

                                <tr>
                                    <td scope="row"><?= $x++ ?></td>
                                    <td scope="row"><?= $off['name'] ?></td>
                                    <td><?= $projects_count ?></td>
                                    <td><?= number_format($projects_amount, 2, '.', ',') ?></td>
                                    <td><?= number_format($projects_payments, 2, '.', ',') ?></td>
                                    <td>
                                        Active: <?= ($projects_active) ?>
                                        Hold: <?= ($projects_hold) ?>
                                        Completed: <?= ($projects_completed) ?>
                                    </td>
                                    <td>
                                        <a href="<?= base_url() ?>report_pro_officers_view/<?= $off['ucode']; ?>" class="btn btn-info btn-sm"> View <i class="fa fa-angle-right" aria-hidden="true"></i> </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>



    </div>
    <!-- /.row -->

    <script>
        function generatePDF() {

            // Options
            var opt = {
                margin: 0.5,
                filename: '<?= $title ?>.pdf',
                image: {
                    type: 'jpeg',
                    quality: 1.98
                },
                html2canvas: {
                    dpi: 200,
                    letterRendering: true,
                    useCORS: true
                },
                jsPDF: {
                    unit: 'in',
                    format: 'A3',
                    orientation: 'landscape'
                }
            };

            // New Promise-based usage:
            // html2pdf().set(opt).from('document.body').save();

            // Get the <ul> element
            const list = document.querySelector('#printpdf');

            // Generate PDF from <ul> only  
            html2pdf().from(list).save();

        }
    </script>


</div>




<?= $this->endSection(); ?>