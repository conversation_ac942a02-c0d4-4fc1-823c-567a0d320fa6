<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>


<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $pro['name'] ?></h1>
                <h5><?= $pro['procode'] ?></h5>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>open_projects/<?= $pro['ucode'] ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> View Project</a></li>
                    <li class="breadcrumb-item active"><?= $pro['name'] ?></li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">
    <div class="row p-2">
        <div class=" col-md-12">
            <div class="card">
                <div class="card-header bg-info ">

                    <a class="btn btn-dark btn-sm " href="<?= base_url() ?>open_projects/<?= $pro['ucode'] ?>"><i class="fa fa-arrow-circle-left " aria-hidden="true"></i>
                        Back
                    </a>

                    <span class=" float-right">Edit Contractor</span>

                </div>
                <?= form_open_multipart("update_projects_contractors") ?>
                <div class="card-body">

                    <div class="row">

                        <div class="form-group col-md-6 ">
                            <label for="">Contactor</label>
                            <select class="form-control" name="contractor" id="">
                                <option selected value="<?= $pro['contractor_id'] ?>"><b><?= $pro['contractor_name'] ?></b></option>
                                <?php foreach ($contractors as $ct) : ?>
                                    <option value="<?= $ct['id'] ?>"><?= $ct['concode'] ?>|<?= $ct['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group col-md-6">
                            <label for="exampleInputFile">Contact Documents <small>Contract Approval, Authorization Letter, etc...</small> </label>
                            <div class="input-group">
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" name="contract_file" id="exampleInputFile" accept=".pdf" required>
                                    <label class="custom-file-label" for="exampleInputFile">Choose Contract Documents
                                    </label>
                                </div>

                            </div>
                            <small>All scanned into single .pdf file</small>

                        </div>

                    </div>

                </div>
                <!-- .body -->
                <div class="card-footer">
                    <input type="hidden" name="pro_ucode" value="<?= $pro['ucode'] ?>">
                    <input type="hidden" name="pro_id" value="<?= $pro['id'] ?>">
                    <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                    <button type="submit" class="btn btn-info float-right"> <i class="fas fa-save    "></i> Save Changes</button>
                </div>

                </form>


            </div>
            <!-- .card -->


            <div class="callout callout-info">
                <h5 class=" text-left"><?= $pro['contractor_code'] ?> - <?= $pro['contractor_name'] ?></h5>

                <span class=" text-right">
                    <b>Contract File: </b>
                    <?php if (!empty($pro['contract_file'])) : ?>
                        <a href="<?= base_url() ?><?= $pro['contract_file'] ?>" class=" btn btn-default btn-sm"><i class="fa fa-download" aria-hidden="true"></i> Download </a>
                    <?php endif; ?>
                </span>
            </div>

        </div>
        <!--.col -->
    </div>
    <!--.row -->
</section>


</body>




<?= $this->endSection() ?>