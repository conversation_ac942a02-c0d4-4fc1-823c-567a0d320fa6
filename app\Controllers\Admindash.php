<?php

namespace App\Controllers;

use App\Models\countryModel;
use App\Models\districtModel;
use App\Models\eventfilesModel;
use App\Models\eventsModel;
use App\Models\llgModel;
use App\Models\orgModel;
use App\Models\prodocsModel;
use App\Models\proeventsModel;
use App\Models\profundModel;
use App\Models\project_officersModel;
use App\Models\projectsModel;
use App\Models\promilefilesModel;
use App\Models\promilestonesModel;
use App\Models\prophasesModel;
use App\Models\provinceModel;
use App\Models\usersModel;

class Admindash extends BaseController
{
    public $session;
    public $usersModel;
    public $pro_officersModel;
    public $projectsModel;
    public $prophasesModel;
    public $promilestonesModel;
    public $promilefilesModel;
    public $countryModel;
    public $provinceModel;
    public $districtModel;
    public $llgModel;
    public $prodocsModel;
    public $profundModel;
    public $proeventsModel;
    public $proeventFilesModel;
    public $orgModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        $this->usersModel = new usersModel();
        $this->pro_officersModel = new project_officersModel();
        $this->projectsModel = new projectsModel();
        $this->prophasesModel = new prophasesModel();
        $this->promilestonesModel = new promilestonesModel();
        $this->promilefilesModel = new promilefilesModel();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->llgModel = new llgModel();
        $this->prodocsModel = new prodocsModel();
        $this->profundModel = new profundModel();
        $this->proeventsModel = new proeventsModel();
        $this->proeventFilesModel = new eventfilesModel();
        $this->orgModel = new orgModel();
    }

    public function index()
    {
        $data['title'] = "Dashboard";
        $data['menu'] = "dashboard";

        $data['org'] = $this->orgModel->where('orgcode', session('orgcode'))->first();
        $data['projects'] = $this->projectsModel->where('orgcode', session('orgcode'))->find();
        $data['payments'] = $this->profundModel->where('orgcode', session('orgcode'))->find();
        $data['milestones'] = $this->promilestonesModel->where('orgcode', session('orgcode'))->find();

        $data['pro_active'] = $this->projectsModel->where('orgcode', session('orgcode'))->where('status', 'active')->find();
        $data['pro_completed'] = $this->projectsModel->where('orgcode', session('orgcode'))->where('status', 'completed')->find();
        $data['pro_hold'] = $this->projectsModel->where('orgcode', session('orgcode'))->where('status', 'hold')->find();
        $data['pro_canceled'] = $this->projectsModel->where('orgcode', session('orgcode'))->where('status', 'canceled')->find();


        //calculations
        $data['pro_total_overpaid'] = $data['pro_total_outstanding'] = $data['pro_total_budget'] = $data['pro_total_paid'] = 0;
        $data['pro_ms_pending'] = $data['pro_ms_completed'] = $data['pro_ms_hold'] = $data['pro_ms_canceled'] = $data['pro_payments'] = 0;


        $projectsID = array();
        foreach ($data['projects'] as $pro) {
            $projectsID[] = $pro['procode'];
        }

        //paymenents
        $data['payments'] = $this->profundModel
            ->whereIn('procode', $projectsID)
            ->where('orgcode', session('orgcode'))->find();

        //milestones
        $data['milestones'] = $this->promilestonesModel
            ->whereIn('procode', $projectsID)
            ->where('orgcode', session('orgcode'))->find();

        //payments totals
        foreach ($data['projects'] as $pay) {
            $data['pro_total_budget'] += checkZero($pay['budget']);
            $data['pro_total_paid'] += checkZero($pay['payment_total']);
            $data['pro_total_outstanding'] += checkZero($pay['budget']) - checkZero($pay['payment_total']);
            if (checkZero($pay['payment_total']) > checkZero($pay['budget'])) {
                $data['pro_total_overpaid'] += checkZero($pay['payment_total']) - checkZero($pay['budget']);
            }
        }

        //milestones totals
        foreach ($data['milestones'] as $ms) {
            if ($ms['checked'] == 'pending') {
                $data['pro_ms_pending'] += 1;
            }
            if ($ms['checked'] == 'completed') {
                $data['pro_ms_completed'] += 1;
            }
            if ($ms['checked'] == 'hold') {
                $data['pro_ms_hold'] += 1;
            }
            if ($ms['checked'] == 'canceled') {
                $data['pro_ms_canceled'] += 1;
            }
        }

        //payment trends
        $data['paydates'] = array();
        foreach ($data['payments'] as $pay) {
            $dateString = $pay['paymentdate'];
            $month = date("m", strtotime($dateString));
            $data['paydates'][] = $month; // Output: 03

        }
        //print_r($data['paydates']);
        $data['paydates'] = array_count_values($data['paydates']);

        echo view('admindash/dashboard', $data);
    }


    public function my_account()
    {
        $data['title'] = "My Account";
        $data['menu'] = "my_account";

        $data['myacc'] = $this->orgModel->where('orgcode', session('orgcode'))->first();
        $data['set_country'] = $this->countryModel->where('code', COUNTRY_CODE)->first();
        $data['get_provinces'] = $this->provinceModel->where('country_id', $data['set_country']['id'])->find();
        $data['admins'] = $this->usersModel->where('orgcode', $data['myacc']['orgcode'])->find();

        $data['get_province'] = $this->provinceModel->where('provincecode', $data['myacc']['province_code'])->first();
        $data['get_district'] = $this->districtModel->where('districtcode', $data['myacc']['district_code'])->first();
        $data['get_llg'] = $this->llgModel->where('llgcode', $data['myacc']['llg_code'])->first();

        echo view('admindash/my_account', $data);
    }

    public function update_admin_orglogo()
    {
        $id = $this->request->getPost('id');
        $concode = $this->request->getPost('concode');

        // doc file
        $docfile = $this->request->getFile('logo_file');

        if ($docfile && $docfile->isValid()) {

            // Generate a custom name for the file
            $newName = 'org_logo' . $concode . "_" . time() . "." . $docfile->getExtension();
            // Move uploaded file to the public/uploads directory
            $docfile->move(ROOTPATH . 'public/uploads/org_files/', $newName);
            // Save file path to database
            $data = [

                'orglogo' => 'public/uploads/org_files/' . $newName,
                'update_by' => session('name'),
            ];
            $this->orgModel->update($id, $data);
        }

        return redirect()->back()->with('success', 'Logo Updated!');
    }


    public function update_admin_orginfo()
    {
        $id = $this->request->getPost('id');

        $get_country = $this->countryModel->where('code', $this->request->getPost('country'))->first();
        $get_province = $this->provinceModel->where('provincecode', $this->request->getPost('province'))->first();
        $get_district = $this->districtModel->where('districtcode', $this->request->getPost('district'))->first();
        $get_llg = $this->llgModel->where('llgcode', $this->request->getPost('llg'))->first();

        $data = [

            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
            'country_code' => $this->request->getPost('country'),
            'province_code' => $this->request->getPost('province'),
            'district_code' => $this->request->getPost('district'),
            'llg_code' => $this->request->getPost('llg'),
            'country_name' => $get_country['name'],
            'province_name' => $get_province['name'],
            'district_name' => $get_district['name'],
            'llg_name' => $get_llg['name'],
            'update_by' => session('name'),
        ];
        $this->orgModel->update($id, $data);

        return redirect()->back()->with('success', 'Logo Updated!');
    }


    public function reports_dashboard()
    {
        $data['title'] = "Reports Dashboard";
        $data['menu'] = "reports_dashboard";

        $data['org'] = $this->orgModel->where('orgcode', session('orgcode'))->orderBy('id', 'asc')->first();
        
        $data['projects'] = $this->projectsModel->where('orgcode', session('orgcode'))->orderBy('name', 'asc')->find();
        
        $data['country'] = $this->countryModel->orderBy('name', 'asc')->find();
        $data['province'] = $this->provinceModel->orderBy('name', 'asc')->find();
        $data['district'] = $this->districtModel->orderBy('name', 'asc')->find();
        $data['llg'] = $this->llgModel->orderBy('name', 'asc')->find();

        //calculations
        $data['pro_total_overpaid'] = $data['pro_total_outstanding'] = $data['pro_total_budget'] = $data['pro_total_paid'] = 0;
        $data['pro_ms_pending'] = $data['pro_ms_completed'] = $data['pro_ms_hold'] = $data['pro_ms_canceled'] = $data['pro_payments'] = 0;



        $country = $province = $district = $llg = $projectsID = array();
        foreach ($data['projects'] as $pro) {
            $projectsID[] = $pro['procode'];
            $country[] = $pro['country'];
            $province[] = $pro['province'];
            $district[] = $pro['district'];
            $llg[] = $pro['llg'];
        }

        //paymenents
        $data['payments'] = $this->profundModel
            ->whereIn('procode', $projectsID)
            ->where('orgcode', session('orgcode'))->find();

        //country 
        $data['country'] = $this->countryModel
            ->whereIn('code', $country)
            ->find();

        //province
        $data['province'] = $this->provinceModel
            ->whereIn('provincecode', $province)
            ->find();

        //district
        $data['district'] = $this->districtModel
            ->whereIn('districtcode', $district)
            ->find();

        //llg
        $data['llg'] = $this->llgModel
            ->whereIn('llgcode', $llg)
            ->find();

        //milestones
        $data['milestones'] = $this->promilestonesModel
            ->whereIn('procode', $projectsID)
            ->where('orgcode', session('orgcode'))->find();

        //phases
        $data['phases'] = $this->prophasesModel
            ->whereIn('procode', $projectsID)
            ->where('orgcode', session('orgcode'))->find();

        //payments totals
        /* foreach ($data['projects'] as $pay) {
            $data['pro_total_budget'] += checkZero($pay['budget']);
            $data['pro_total_paid'] += checkZero($pay['payment_total']);
            $data['pro_total_outstanding'] += checkZero($pay['budget']) - checkZero($pay['payment_total']);
            if (checkZero($pay['payment_total']) > checkZero($pay['budget'])) {
                $data['pro_total_overpaid'] += checkZero($pay['payment_total']) - checkZero($pay['budget']);
            }
        } */

        //milestones totals
        /* foreach ($data['milestones'] as $ms) {
            if ($ms['checked'] == 'pending') {
                $data['pro_ms_pending'] += 1;
            }
            if ($ms['checked'] == 'completed') {
                $data['pro_ms_completed'] += 1;
            }
            if ($ms['checked'] == 'hold') {
                $data['pro_ms_hold'] += 1;
            }
            if ($ms['checked'] == 'hold') {
                $data['pro_ms_canceled'] += 1;
            }
        } */

        echo view('admindash/reports_dashboard', $data);
    }
}
