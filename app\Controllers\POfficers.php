<?php

namespace App\Controllers;

use App\Models\countryModel;
use App\Models\districtModel;
use App\Models\eventfilesModel;
use App\Models\eventsModel;
use App\Models\kmlfilesModel;
use App\Models\llgModel;
use App\Models\prodocsModel;
use App\Models\proeventsModel;
use App\Models\profundModel;
use App\Models\project_officersModel;
use App\Models\projectsModel;
use App\Models\promilefilesModel;
use App\Models\promilestonesModel;
use App\Models\prophasesModel;
use App\Models\provinceModel;
use App\Models\usersModel;

class POfficers extends BaseController
{
    public $session;
    public $usersModel;
    public $pro_officersModel;
    public $projectsModel;
    public $prophasesModel;
    public $promilestonesModel;
    public $promilefilesModel;
    public $countryModel;
    public $provinceModel;
    public $districtModel;
    public $llgModel;
    public $prodocsModel;
    public $profundModel;
    public $proeventsModel;
    public $proeventFilesModel;
    public $kmlfilesModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        $this->usersModel = new usersModel();
        $this->pro_officersModel = new project_officersModel();
        $this->projectsModel = new projectsModel();
        $this->prophasesModel = new prophasesModel();
        $this->promilestonesModel = new promilestonesModel();
        $this->promilefilesModel = new promilefilesModel();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->llgModel = new llgModel();
        $this->prodocsModel = new prodocsModel();
        $this->profundModel = new profundModel();
        $this->proeventsModel = new proeventsModel();
        $this->proeventFilesModel = new eventfilesModel();
        $this->kmlfilesModel = new kmlfilesModel();
    }

    public function index()
    {
        $data['title'] = "Dashboard";
        $data['menu'] = "dashboard";

        $data['projects'] = $this->projectsModel->where('pro_officer_id', session('userid'))->where('orgcode', session('orgcode'))->orderBy('name', 'asc')->find();
        $data['milestones'] = $this->promilestonesModel->where('orgcode', session('orgcode'))->orderBy('milestones', 'asc')->find();

        echo view('pofficers/dash', $data);
    }

    public function po_open_project($ucode)
    {


        $data['pro'] = $this->projectsModel->where('ucode', $ucode)->first();

        $data['title'] = $data['pro']['procode'];
        $data['menu'] = "dashboard";
        echo view('pofficers/po_open_project', $data);
    }

    public function po_details($ucode)
    {

        $data['pro'] = $this->projectsModel->where('ucode', $ucode)->first();
        $data['country'] = $this->countryModel->where('code', $data['pro']['country'])->first();
        $data['province'] = $this->provinceModel->where('provincecode', $data['pro']['province'])->first();
        $data['dist'] = $this->districtModel->where('districtcode', $data['pro']['district'])->first();
        $data['kmls'] = $this->kmlfilesModel->where('proucode', $ucode)->find();


        //edit load
        $data['set_country'] = $this->countryModel->where('code', COUNTRY_CODE)->first();
        $data['get_provinces'] = $this->provinceModel->where('country_id', $data['set_country']['id'])->find();
        $data['get_district'] = $this->districtModel->where('districtcode', $data['pro']['district'])->first();


        $data['title'] = "dt" . $data['pro']['procode'];
        $data['menu'] = "dashboard";
        echo view('pofficers/po_details', $data);
    }

    public function po_details_info_edit($ucode)
    {

        $data['pro'] = $this->projectsModel->where('ucode', $ucode)->first();
        $data['country'] = $this->countryModel->where('code', $data['pro']['country'])->first();
        $data['province'] = $this->provinceModel->where('provincecode', $data['pro']['province'])->first();
        $data['dist'] = $this->districtModel->where('districtcode', $data['pro']['district'])->first();
        $data['kmls'] = $this->kmlfilesModel->where('proucode', $ucode)->find();


        //edit load
        $data['set_country'] = $this->countryModel->where('code', COUNTRY_CODE)->first();
        $data['get_provinces'] = $this->provinceModel->where('country_id', $data['set_country']['id'])->find();

        $data['title'] = "edit" . $data['pro']['procode'];
        $data['menu'] = "dashboard";
        echo view('pofficers/po_details_info_edit', $data);
    }



    public function getdistricts()
    {
        $prov = $this->request->getPost('provinceid');
        $districts = $this->districtModel->where('province_id', $prov)->find();

        return $this->response->setJSON($districts);
    }

    public function po_phases($ucode)
    {

        $data['pro'] = $this->projectsModel->where('ucode', $ucode)->first();
        $data['phases'] = $this->prophasesModel->where('procode', $data['pro']['procode'])->find();
        $data['milestones'] = $this->promilestonesModel->where('procode', $data['pro']['procode'])->find();

        $data['title'] = "ph" . $data['pro']['procode'];
        $data['menu'] = "dashboard";
        echo view('pofficers/po_phases', $data);
    }

    public function po_milestones($ucode)
    {

        $data['milestones'] = $this->promilestonesModel->where('ucode', $ucode)->first();
        $data['pro'] = $this->projectsModel->where('procode', $data['milestones']['procode'])->first();
        $data['phases'] = $this->prophasesModel->where('id', $data['milestones']['phase_id'])->first();
        $data['milefiles'] = $this->promilefilesModel->where('milestones_id', $data['milestones']['id'])->find();

        $data['title'] = "ms" . $data['pro']['procode'];
        $data['menu'] = "dashboard";
        echo view('pofficers/po_milestones', $data);
    }

    public function milestone_notes()
    {

        $id = $this->request->getVar('ms_id');
        $check = $this->request->getVar('check');

        $data = [

            'notes' => $this->request->getVar('milenotes'),
            'checked' => $check,
            'checked_date' => $this->request->getVar('milesdate'),
            'update_by' => session('name'),
        ];

        $this->promilestonesModel->update($id, $data);

        return redirect()->back()->with('success', 'Notes Saved');
    }

    public function milestone_files()
    {

        $files = $this->request->getFileMultiple('milefiles');
        $procode = $this->request->getVar('procode');
        $ph_id = $this->request->getVar('ph_id');
        $ms_id = $this->request->getVar('ms_id');

        // echo "<pre>";
        //  print_r($files);

        $x = 1;

        foreach ($files as $file) {
            if ($file->isValid()) {

                // Generate a custom name for the file
                $newName = "mf_" . $procode . "_" . time() . "." . $file->getExtension();

                // echo $newName;

                // Move the uploaded file to the uploads folder with the new name
                $file->move(ROOTPATH . 'public/uploads/milestone_files/', $newName);


                // Insert the file path into the database
                $data = [
                    'ucode' => uniqid() . time(),
                    'orgcode' => session('orgcode'),
                    'procode' => $procode,
                    'milestones_id' => $ms_id,
                    'phase_id' => $ph_id,
                    'filepath' => 'public/uploads/milestone_files/' . $newName,
                    'create_by' => session('name'),
                    'status' => 1,
                ];
                $this->promilefilesModel->insert($data);
            }
        }
        return redirect()->back()->with('success', 'Files Uploaded');
    }

    public function po_files_open($ucode)
    {

        $data['pro'] = $this->projectsModel->where('ucode', $ucode)->first();
        $data['prodocs'] = $this->prodocsModel->where('procode', $data['pro']['procode'])->find();

        $data['title'] = "fl" . $data['pro']['procode'];
        $data['menu'] = "dashboard";
        echo view('pofficers/po_files_open', $data);
    }


    public function po_funding_open($ucode)
    {

        $data['pro'] = $this->projectsModel->where('ucode', $ucode)->first();
        $data['fund'] = $this->profundModel->where('procode', $data['pro']['procode'])->where('orgcode', session('orgcode'))->orderBy('paymentdate', 'desc')->find();

        $data['title'] = "fd" . $data['pro']['procode'];
        $data['menu'] = "dashboard";
        echo view('pofficers/po_funding_open', $data);
    }


    public function po_events_open($ucode)
    {

        $data['pro'] = $this->projectsModel->where('ucode', $ucode)->first();
        $data['proevents'] = $this->proeventsModel->where('procode', $data['pro']['procode'])->where('orgcode', session('orgcode'))->orderBy('eventdate', 'desc')->find();
        $data['evfiles'] = $this->proeventFilesModel->where('procode', $data['pro']['procode'])->where('orgcode', session('orgcode'))->find();

        $data['title'] = "ev" . $data['pro']['procode'];
        $data['menu'] = "dashboard";
        echo view('pofficers/po_events_open', $data);
    }

    public function po_reports_open($ucode)
    {

        $data['pro'] = $this->projectsModel->where('ucode', $ucode)->first();
        $data['events'] = $this->proeventsModel->where('procode', $data['pro']['procode'])->where('orgcode', session('orgcode'))->orderBy('eventdate', 'asc')->find();
        $data['evfiles'] = $this->proeventFilesModel->where('procode', $data['pro']['procode'])->where('orgcode', session('orgcode'))->find();
        $data['payments'] = $this->profundModel->where('procode', $data['pro']['procode'])->where('orgcode', session('orgcode'))->orderBy('paymentdate', 'asc')->find();

        $data['prodocs'] = $this->prodocsModel->where('procode', $data['pro']['procode'])->find();
        $data['phases'] = $this->prophasesModel->where('procode', $data['pro']['procode'])->find();
        $data['milestones'] = $this->promilestonesModel->where('procode', $data['pro']['procode'])->find();
        //address
        $data['country'] = $this->countryModel->where('code', $data['pro']['country'])->first();
        $data['province'] = $this->provinceModel->where('provincecode', $data['pro']['province'])->first();
        $data['dist'] = $this->districtModel->where('districtcode', $data['pro']['district'])->first();
        $data['llg'] = $this->llgModel->where('llgcode', $data['pro']['llg'])->first();


        $data['pro_ms_pending'] = $data['pro_ms_completed'] = $data['pro_ms_hold'] = $data['pro_ms_canceled'] = 0;
        //calculations
        foreach ($data['milestones'] as $ms) {
            if ($ms['checked'] == 'pending') {
                $data['pro_ms_pending']+=1;
            }
            if ($ms['checked'] == 'completed') {
                $data['pro_ms_completed']+=1;
            }
            if ($ms['checked'] == 'hold') {
                $data['pro_ms_hold']+=1;
            }
            if ($ms['checked'] == 'canceled') {
                $data['pro_ms_canceled']+=1;
            }
        }
        
        //percentages = milestones completed / total milestones
        $data['ms_completed_percent'] = ($data['pro_ms_completed'] / count($data['milestones'])) * 100;
        $data['ms_pending_percent'] = ($data['pro_ms_pending'] / count($data['milestones'])) * 100;
        $data['ms_hold_percent'] = ($data['pro_ms_hold'] / count($data['milestones'])) * 100;
        $data['ms_canceled_percent'] = ($data['pro_ms_canceled'] / count($data['milestones'])) * 100;
        




        $data['title'] = "rep" . $data['pro']['procode'];
        $data['menu'] = "dashboard";
        echo view('pofficers/po_reports_open', $data);
    }
}
