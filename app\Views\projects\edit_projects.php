<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>


<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $pro['name'] ?></h1>
                <h5><?= $pro['procode'] ?></h5>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>open_projects/<?= $pro['ucode'] ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> View Project</a></li>
                    <li class="breadcrumb-item active"><?= $pro['name'] ?></li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">
    <div class="row p-2">
        <div class=" col-md-12">

            <div class="card">
                <div class="card-header bg-info">
                    <a href="<?= base_url() ?>open_projects/<?= $pro['ucode'] ?>" class="btn btn-dark btn-sm"><i class="fa fa-arrow-circle-left " aria-hidden="true"></i> Back </a>

                    <div class="float-right">
                        Edit Project Information
                    </div>

                </div>
                <?= form_open() ?>
                <div class="card-body">

                    <div class="row">
                        <!-- <div class="form-group col-md-4">
                           <input type="text" class=" form-control" name="roadcode" id="roadcode" readonly required placeholder="Road Code">
                        </div> -->

                        <div class="form-group col-md-8">
                            <?= form_input('name',  $pro['name'], ['class' => 'form-control', 'placeholder' => 'Enter Name', 'required' => 'required']) ?>
                        </div>
                        <div class="form-group col-md-4">
                            <input type="date" class="form-control" name="pro_date" placeholder="Commence Date" value="<?= $pro['pro_date'] ?>">
                        </div>
                        <div class="form-group col-md-12">
                            <input type="text" class="form-control" name="pro_site" placeholder="Project Site" value="<?= $pro['pro_site'] ?>">
                        </div>

                        <div class="form-group col-md-12">
                            <?= form_textarea('description', $pro['description'], ['class' => 'form-control', 'placeholder' => 'Enter Description']) ?>
                        </div>
                        <div class="form-group col-md-3">
                            <select name="country" id="country" class="form-control">
                                <option selected value="<?= $pro['country'] ?>"><?= $set_country['name'] ?></option>
                            </select>

                        </div>
                        <div class="form-group col-md-3">
                            <select name="province" id="province" class="form-control">
                                <?php foreach ($get_provinces as $prov) :
                                    if ($pro['province'] == $prov['provincecode']) :
                                ?>
                                        <option selected value="<?= $prov['provincecode'] ?>"><?= $prov['name'] ?></option>
                                    <?php
                                    else :
                                    ?>
                                        <option value="<?= $prov['provincecode'] ?>"><?= $prov['name'] ?></option>
                                    <?php
                                    endif;
                                    ?>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <select name="district" id="district" class="form-control">
                                <option selected value="<?= $pro['district'] ?>"><?= $get_district['name'] ?></option>
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <select name="llg" id="llg" class="form-control">
                                <?php if (!empty($pro['llg'])) : ?>
                                    <option selected value="<?= $pro['llg'] ?>"><?= $get_llg['name'] ?></option>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-footer">

                    <input type="hidden" name="proid" value="<?= $pro['id'] ?>">
                    <button type="submit" class="btn btn-info float-right"> <i class="fas fa-save    "></i> SAVE CHANGES </button>
                </div>
                <?= form_close() ?>
            </div>

        </div>
    </div>
</section>


</body>

<script>
    $(document).ready(function() {
        $('#province').change(function() {
            var province_code = $(this).val();

            $.ajax({
                url: '<?= base_url() ?>getaddress',
                type: 'post',
                data: {
                    province_code: province_code
                },
                dataType: 'json',
                success: function(response) {
                    console.log(response);
                    var len = response.district.length;

                    $("#district").empty();
                    $("#district").append("<option value=''>Select a District</option>");

                    for (var i = 0; i < len; i++) {
                        var code = response.district[i]['districtcode'];
                        var name = response.district[i]['name'];
                        //var code = response.subcategories[i]['code'];

                        $("#district").append("<option value='" + code + "'>" + name +
                            "</option>");

                    }
                }
            });
        });



        $('#district').change(function() {
            var district_code = $(this).val();

            $.ajax({
                url: '<?= base_url() ?>getaddress',
                type: 'post',
                data: {
                    district_code: district_code
                },
                dataType: 'json',
                success: function(response) {
                    console.log(response);
                    var len = response.llgs.length;
                    $("#llg").empty();
                    $("#llg").append("<option value=''>Select a LLG</option>");
                    for (var i = 0; i < len; i++) {

                        var code = response.llgs[i]['llgcode'];
                        var name = response.llgs[i]['name'];

                        $("#llg").append("<option value='" + code + "'>" + name + "</option>");
                    }
                }
            });
        });


    });
</script>



<?= $this->endSection() ?>