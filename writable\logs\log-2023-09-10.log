INFO - 2023-09-10 23:22:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:22:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:27:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:28:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:28:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:28:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2023-09-10 23:28:26 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in C:\xampp\htdocs\promis\system\Database\MySQLi\Connection.php:173
Stack trace:
#0 C:\xampp\htdocs\promis\system\Database\MySQLi\Connection.php(173): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'promis_db', 3306, '', 0)
#1 C:\xampp\htdocs\promis\system\Database\BaseConnection.php(391): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\promis\system\Database\BaseConnection.php(573): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\promis\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\promis\system\Model.php(268): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\promis\system\BaseModel.php(656): CodeIgniter\Model->doFirst()
#6 C:\xampp\htdocs\promis\app\Controllers\POfficers.php(72): CodeIgniter\BaseModel->first()
#7 C:\xampp\htdocs\promis\system\CodeIgniter.php(934): App\Controllers\POfficers->po_open_project('64c9bd1b0b90316...')
#8 C:\xampp\htdocs\promis\system\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\POfficers))
#9 C:\xampp\htdocs\promis\system\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\promis\index.php(67): CodeIgniter\CodeIgniter->run()
#11 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in C:\xampp\htdocs\promis\system\Database\MySQLi\Connection.php:218
Stack trace:
#0 C:\xampp\htdocs\promis\system\Database\BaseConnection.php(391): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\promis\system\Database\BaseConnection.php(573): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\promis\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\xampp\htdocs\promis\system\Model.php(268): CodeIgniter\Database\BaseBuilder->get()
#4 C:\xampp\htdocs\promis\system\BaseModel.php(656): CodeIgniter\Model->doFirst()
#5 C:\xampp\htdocs\promis\app\Controllers\POfficers.php(72): CodeIgniter\BaseModel->first()
#6 C:\xampp\htdocs\promis\system\CodeIgniter.php(934): App\Controllers\POfficers->po_open_project('64c9bd1b0b90316...')
#7 C:\xampp\htdocs\promis\system\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\POfficers))
#8 C:\xampp\htdocs\promis\system\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\promis\index.php(67): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2023-09-10 23:28:26 --> Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
in SYSTEMPATH\Database\BaseConnection.php on line 427.
 1 SYSTEMPATH\Database\BaseConnection.php(573): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `projects`
WHERE `ucode` = :ucode:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(268): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(656): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\POfficers.php(72): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\POfficers->po_open_project('64c9bd1b0b9031690942747')
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\POfficers))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-10 23:28:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:28:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:28:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:29:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:30:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:30:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:31:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:31:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:31:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:32:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:32:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:32:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:32:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:32:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:32:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:33:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:33:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:34:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:38:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:38:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:40:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:43:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:46:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:54:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:54:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-10 23:58:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
