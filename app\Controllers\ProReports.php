<?php

namespace App\Controllers;

use App\Models\contractorsModel;
use App\Models\contractorsNoticesModel;
use App\Models\countryModel;
use App\Models\districtModel;
use App\Models\eventfilesModel;
use App\Models\kmlfilesModel;
use App\Models\llgModel;
use App\Models\prodocsModel;
use App\Models\proeventsModel;
use App\Models\profundModel;
use App\Models\project_officersModel;
use App\Models\projectsModel;
use App\Models\promilestonesModel;
use App\Models\prophasesModel;
use App\Models\provinceModel;
use App\Models\selectionModel;
use App\Models\settingsModel;
use App\Models\usersModel;


class ProReports extends BaseController
{
    public $session;
    public $usersModel;

    public $countryModel;
    public $provinceModel;
    public $districtModel;
    public $llgModel;
    public $settingsModel;
    public $selectModel;
    public $projectsModel;
    public $profundModel;
    public $prodocsModel;
    public $prophaseModel;
    public $promilestonesModel;
    public $proeventsModel;
    public $eventfilesModel;
    public $contractorsModel;
    public $project_officersModel;
    public $kmlfilesModel;
    public $proFilesModel;
    public $conNoticeModel;


    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        $this->usersModel = new usersModel();

        $this->countryModel = new countryModel();
        $this->settingsModel = new settingsModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->llgModel = new llgModel();
        $this->selectModel = new selectionModel();
        $this->projectsModel = new projectsModel();
        $this->profundModel = new profundModel();
        $this->prodocsModel = new prodocsModel();
        $this->prophaseModel = new prophasesModel();
        $this->promilestonesModel = new promilestonesModel();
        $this->proeventsModel = new proeventsModel();
        $this->eventfilesModel = new eventfilesModel();
        $this->contractorsModel = new contractorsModel();
        $this->project_officersModel = new project_officersModel();
        $this->kmlfilesModel = new kmlfilesModel();
        $this->proFilesModel = new prodocsModel();
        $this->conNoticeModel = new contractorsNoticesModel();
    }

    /* 
    * index
    * create_road
    */

    public function index()
    {
        $data['title'] = "Projects Report";
        $data['menu'] = "reports";

        $data['projects'] = $this->projectsModel->where('orgcode', session('orgcode'))->find();
        $data['payments'] = $this->profundModel->where('orgcode', session('orgcode'))->find();
        $data['milestones'] = $this->promilestonesModel->where('orgcode', session('orgcode'))->find();

        $data['pro_active'] = $this->projectsModel->where('orgcode', session('orgcode'))->where('status', 'active')->find();
        $data['pro_completed'] = $this->projectsModel->where('orgcode', session('orgcode'))->where('status', 'completed')->find();
        $data['pro_hold'] = $this->projectsModel->where('orgcode', session('orgcode'))->where('status', 'hold')->find();

        //calculations
        /* $data['pro_total_budget'] = 0;
        foreach ($data['projects'] as $pro) {
            $data['pro_total_budget'] += $pro['budget'];
        }

        $data['pro_total_paid'] = 0;
        foreach ($data['payments'] as $pro) {
            $data['pro_total_paid'] += $pro['amount'];
        }

        $data['pro_ms_pending'] = $data['pro_ms_completed'] = $data['pro_ms_hold'] = array();
        foreach ($data['milestones'] as $pro) {

            if ($pro['checked'] == 'pending') {
                $data['pro_ms_pending'][] = $pro['checked'];
            }
            if ($pro['checked'] == 'completed') {
                $data['pro_ms_completed'][] = $pro['checked'];
            }
            if ($pro['checked'] == 'hold') {
                $data['pro_ms_hold'][] = $pro['checked'];
            }
        } */
        
         //calculations
         $data['pro_total_overpaid'] = $data['pro_total_outstanding'] = $data['pro_total_budget'] = $data['pro_total_paid'] = 0;
         $data['pro_ms_pending'] = $data['pro_ms_completed'] = $data['pro_ms_hold'] = $data['pro_payments'] = 0;
 
 
         $projectsID = array();
         foreach ($data['projects'] as $pro) {
             $projectsID[] = $pro['procode'];
         }
 
         //paymenents
         $data['payments'] = $this->profundModel
             ->whereIn('procode', $projectsID)
             ->where('orgcode', session('orgcode'))->find();
 
         //milestones
         $data['milestones'] = $this->promilestonesModel
             ->whereIn('procode', $projectsID)
             ->where('orgcode', session('orgcode'))->find();
 
         //payments totals
         foreach ($data['projects'] as $pay) {
             $data['pro_total_budget'] += checkZero($pay['budget']);
             $data['pro_total_paid'] += checkZero($pay['payment_total']);
             $data['pro_total_outstanding'] += checkZero($pay['budget']) - checkZero($pay['payment_total']);
             if (checkZero($pay['payment_total']) > checkZero($pay['budget'])) {
                 $data['pro_total_overpaid'] += checkZero($pay['payment_total']) - checkZero($pay['budget']);
             }
         }
 
         //milestones totals
         foreach ($data['milestones'] as $ms) {
             if ($ms['checked'] == 'pending') {
                 $data['pro_ms_pending'] += 1;
             }
             if ($ms['checked'] == 'completed') {
                 $data['pro_ms_completed'] += 1;
             }
             if ($ms['checked'] == 'hold') {
                 $data['pro_ms_hold'] += 1;
             }
         }
 


        echo view('pro_reports/report_projects_dash', $data);
    }


    public function report_projects_status($status)
    {

        if ($status != 'all') {
            $data['projects'] = $this->projectsModel->where('orgcode', session('orgcode'))->where('status', $status)->orderBy('name', 'asc')->find();
        } else {
            $data['projects'] = $this->projectsModel->where('orgcode', session('orgcode'))->orderBy('name', 'asc')->find();
        }

        //calculations
        $data['pro_total_overpaid'] = $data['pro_total_outstanding'] = $data['pro_total_budget'] = $data['pro_total_paid'] = 0;
        $data['pro_ms_pending'] = $data['pro_ms_completed'] = $data['pro_ms_hold'] = $data['pro_payments'] = 0;


        $projectsID = array();
        foreach ($data['projects'] as $pro) {
            $projectsID[] = $pro['procode'];
        }

        //paymenents
        $data['payments'] = $this->profundModel
            ->whereIn('procode', $projectsID)
            ->where('orgcode', session('orgcode'))->find();

        //milestones
        $data['milestones'] = $this->promilestonesModel
            ->whereIn('procode', $projectsID)
            ->where('orgcode', session('orgcode'))->find();

        //payments totals
        foreach ($data['projects'] as $pay) {
            $data['pro_total_budget'] += checkZero($pay['budget']);
            $data['pro_total_paid'] += checkZero($pay['payment_total']);
            $data['pro_total_outstanding'] += checkZero($pay['budget']) - checkZero($pay['payment_total']);
            if (checkZero($pay['payment_total']) > checkZero($pay['budget'])) {
                $data['pro_total_overpaid'] += checkZero($pay['payment_total']) - checkZero($pay['budget']);
            }
        }

        //milestones totals
        foreach ($data['milestones'] as $ms) {
            if ($ms['checked'] == 'pending') {
                $data['pro_ms_pending'] += 1;
            }
            if ($ms['checked'] == 'completed') {
                $data['pro_ms_completed'] += 1;
            }
            if ($ms['checked'] == 'hold') {
                $data['pro_ms_hold'] += 1;
            }
        }

        $data['status'] = $status;
        $data['title'] = ucwords($status . " Projects Report ");
        $data['menu'] = "reports";


        echo view('pro_reports/report_projects_status', $data);
    }



    public function report_projects_view($ucode)
    {
        $data['title'] = "Projects Report View";
        $data['menu'] = "reports";


        $data['pro'] = $this->projectsModel->where('orgcode', session('orgcode'))->where('ucode', $ucode)->orderBy('name', 'asc')->first();

        //get address
        $data['get_country'] = $this->countryModel->where('code', $data['pro']['country'])->first();
        $data['get_province'] = $this->provinceModel->where('provincecode', $data['pro']['province'])->first();
        $data['get_district'] = $this->districtModel->where('districtcode', $data['pro']['district'])->first();
        $data['get_llg'] = $this->llgModel->where('llgcode', $data['pro']['llg'])->first();


        //calculations
        $data['pro_total_budget'] = $data['pro_total_paid'] = 0;
        $data['pro_ms_pending'] = $data['pro_ms_completed'] = $data['pro_ms_hold'] = $data['pro_payments'] = array();
        //   foreach ($data['pro'] as $pro) {
        $data['payments'] = $this->profundModel->where('orgcode', session('orgcode'))->where('procode', $data['pro']['procode'])->find();
        $data['milestones'] = $this->promilestonesModel->where('orgcode', session('orgcode'))->where('procode', $data['pro']['procode'])->find();
        $data['milestones_last'] = $this->promilestonesModel->where('orgcode', session('orgcode'))->where('procode', $data['pro']['procode'])->orderBy('update_at', 'desc')->first();
        $data['phases'] = $this->prophaseModel->where('orgcode', session('orgcode'))->where('procode', $data['pro']['procode'])->find();
        $data['pro_files'] = $this->proFilesModel->where('orgcode', session('orgcode'))->where('procode', $data['pro']['procode'])->orderBy('name', 'asc')->find();
        $data['events'] = $this->proeventsModel->where('orgcode', session('orgcode'))->where('procode', $data['pro']['procode'])->find();


        foreach ($data['payments'] as $pay) {
            $data['pro_total_paid'] += $pay['amount'];
            $data['pro_payments'][] = [
                'procode' => $pay['procode'],
                'amount' => $pay['amount'],
            ];
        }

        foreach ($data['milestones'] as $ms) {

            if ($ms['checked'] == 'pending') {
                $data['pro_ms_pending'][] = $ms['checked'];
            }
            if ($ms['checked'] == 'completed') {
                $data['pro_ms_completed'][] = $ms['checked'];
            }
            if ($ms['checked'] == 'hold') {
                $data['pro_ms_hold'][] = $ms['checked'];
            }
        }
        //}

        echo view('pro_reports/report_projects_view', $data);
    }


    public function report_pro_payment_record($id)
    {
        $data['title'] = "Payment Item";
        $data['menu'] = "reports";


        //calculations
        $data['pay'] = $this->profundModel->where('orgcode', session('orgcode'))->where('id', $id)->first();
        //pro
        $data['pro'] = $this->projectsModel->where('orgcode', session('orgcode'))->where('procode', $data['pay']['procode'])->orderBy('name', 'asc')->first();

        echo view('pro_reports/report_pro_payment_record', $data);
    }



    public function report_contractors_dash()
    {
        $data['title'] = "Projects Contractors";
        $data['menu'] = "reports";

        $data['contractors'] = $this->projectsModel
            ->select('projects.contractor_id, contractor_details.*')
            ->join('contractor_details', 'projects.contractor_id = contractor_details.id')
            ->where('projects.orgcode', session('orgcode'))
            ->groupBy('projects.contractor_id')
            ->find();

        $contractorID = array();
        foreach ($data['contractors'] as $contractor) {
            $contractorID[] = $contractor['contractor_id'];
        }

        /* $id_list = array(1, 2, 3, 4, 5);
        $this->db->whereIn('id', $id_list)->where('orgcode', session('orgcode'))->find(); */

        $data['projects'] = $this->projectsModel
            //->select('projects.*, contractor_details.*')
            //->join('contractor_details', 'projects.contractor_id = contractor_details.id')
            ->whereIn('contractor_id', $contractorID)
            ->where('orgcode', session('orgcode'))->find();


        echo view('pro_reports/report_contractors_dash', $data);
    }

    public function report_contractors_view($ucode)
    {

        $data['con'] = $this->contractorsModel->where('ucode', $ucode)->first();

        $data['mynotices'] = $this->conNoticeModel->where('concode', $data['con']['concode'])->where('orgcode', session('orgcode'))->orderBy('notice_date', 'desc')->find();
        $data['notices'] = $this->conNoticeModel->where('concode', $data['con']['concode'])->where('orgcode !=', session('orgcode'))->orderBy('notice_date', 'desc')->find();

        $data['myprojects'] = $this->projectsModel->where('contractor_id', $data['con']['id'])->where('orgcode', session('orgcode'))->find();
        $data['projects'] = $this->projectsModel->where('contractor_id', $data['con']['id'])->where('orgcode !=', session('orgcode'))->find();

        $projectsID = array();
        foreach ($data['myprojects'] as $pro) {
            $projectsID[] = $pro['contractor_id'];
        }

        $data['milestones'] = $this->promilestonesModel
            ->whereIn('procode', $projectsID)->where('orgcode', session('orgcode'))->find();

        $data['title'] = $data['con']['name'];
        $data['menu'] = "reports";
        echo view('pro_reports/report_contractors_view', $data);
    }




    public function report_pro_officers_dash()
    {
        $data['title'] = "Project Officers";
        $data['menu'] = "reports";

        $data['pofficers'] = $this->projectsModel
            ->select('projects.pro_officer_id, project_officers.*')
            ->join('project_officers', 'projects.pro_officer_id = project_officers.id')
            ->where('projects.orgcode', session('orgcode'))
            ->groupBy('projects.pro_officer_id')
            ->find();

        $pOfficersID = array();
        foreach ($data['pofficers'] as $poff) {
            $pOfficersID[] = $poff['pro_officer_id'];
        }

        /* $id_list = array(1, 2, 3, 4, 5);
        $this->db->whereIn('id', $id_list)->where('orgcode', session('orgcode'))->find(); */

        $data['projects'] = $this->projectsModel
            //->select('projects.*, contractor_details.*')
            //->join('contractor_details', 'projects.contractor_id = contractor_details.id')
            ->whereIn('pro_officer_id', $pOfficersID)
            ->where('orgcode', session('orgcode'))->find();


        echo view('pro_reports/report_pro_officers_dash', $data);
    }


    public function report_pro_officers_view($ucode)
    {

        $data['off'] = $this->project_officersModel->where('ucode', $ucode)->first();

        $data['myprojects'] = $this->projectsModel->where('pro_officer_id', $data['off']['id'])->where('orgcode', session('orgcode'))->find();
        $data['projects'] = $this->projectsModel->where('pro_officer_id', $data['off']['id'])->where('orgcode !=', session('orgcode'))->find();


        $data['title'] = $data['off']['name'];
        $data['menu'] = "reports";
        echo view('pro_reports/report_pro_officers_view', $data);
    }
}
