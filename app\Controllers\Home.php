<?php

namespace App\Controllers;

use App\Models\countryModel;
use App\Models\districtModel;
use App\Models\orgModel;
use App\Models\profundModel;
use App\Models\project_officersModel;
use App\Models\projectsModel;
use App\Models\promilestonesModel;
use App\Models\prophasesModel;
use App\Models\provinceModel;
use App\Models\usersModel;

class Home extends BaseController
{
    public $session;
    public $usersModel;
    public $orgModel;
    public $projectsModel;
    public $project_officersModel;
    public $paymentsModel;
    public $milestonesModel;
    public $phasesModel;
    public $countryModel;
    public $provinceModel;
    public $districtModel;


    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        $this->usersModel = new usersModel();
        $this->orgModel = new orgModel();
        $this->projectsModel = new projectsModel();
        $this->project_officersModel = new project_officersModel();
        $this->paymentsModel = new profundModel();
        $this->milestonesModel = new promilestonesModel();
        $this->phasesModel = new prophasesModel();
        $this->districtModel = new districtModel();
        $this->provinceModel = new provinceModel();
        $this->countryModel = new countryModel();
    }

    public function index()
    {
        $data['title'] = "Home";
        $data['menu'] = "home";

        $data['pro'] = $this->projectsModel->findAll();
        $data['milestones'] = $this->milestonesModel->find();
        $data['payments'] = $this->paymentsModel->orderBy('paymentdate', 'asc')->find();

        echo view('home/home', $data);
    }

    public function home_project_one_view($ucode)
    {

        $data['pro'] = $this->projectsModel->where('ucode', $ucode)->first();
        $data['milestones'] = $this->milestonesModel->where('procode', $data['pro']['procode'])->find();

        $data['ms_pending'] = $data['ms_completed'] = $data['ms_hold'] = $data['ms_canceled'] = 0;
        foreach ($data['milestones'] as $ms) {
            if ($ms['checked'] == "pending") {
                $data['ms_pending'] += 1;
            }
            if ($ms['checked'] == "completed") {
                $data['ms_completed'] += 1;
            }
            if ($ms['checked'] == "hold") {
                $data['ms_hold'] += 1;
            }
            if ($ms['checked'] == "canceled") {
                $data['ms_canceled'] += 1;
            }
        }

        // ms percentage = (ms_completed/ms_total)*100
        $data['ms_pending_percentage'] = $data['ms_completed_percentage'] = $data['ms_hold_percentage'] = $data['ms_canceled_percentage'] = 0;
        //check divisible by zero
        if (count($data['milestones']) > 0) {
            //check for zero
            if ($data['ms_pending'] > 0) {
                $data['ms_pending_percentage'] = ($data['ms_pending'] / count($data['milestones'])) * 100;
            } else {
                $data['ms_pending_percentage'] = 0;
            }
            if ($data['ms_completed'] > 0) {
                $data['ms_completed_percentage'] = ($data['ms_completed'] / count($data['milestones'])) * 100;
            } else {
                $data['ms_completed_percentage'] = 0;
            }
            if ($data['ms_hold'] > 0) {
                $data['ms_hold_percentage'] = ($data['ms_hold'] / count($data['milestones'])) * 100;
            } else {
                $data['ms_hold_percentage'] = 0;
            }
            if ($data['ms_canceled'] > 0) {
                $data['ms_canceled_percentage'] = ($data['ms_canceled'] / count($data['milestones'])) * 100;
            } else {
                $data['ms_canceled_percentage'] = 0;
            }
        }

        
        $data['phases'] = $this->phasesModel->where('procode', $data['pro']['procode'])->find();
        //address
        $data['country'] = $this->countryModel->where('code', $data['pro']['country'])->first();
        $data['province'] = $this->provinceModel->where('provincecode', $data['pro']['province'])->first();
        $data['dist'] = $this->districtModel->where('districtcode', $data['pro']['district'])->first();

        $data['title'] = "Project";
        $data['menu'] = "home";
        
        //echo "<pre>";
        //print_r($data['phases']);
        
        echo view('home/home_project_one_view', $data);
    }

    public function about()
    {
        $data['title'] = "About Org.Calendar";
        $data['menu'] = "about";
        echo view('home/about', $data);
    }

    public function login()
    {

        // Check if form has been submitted
        if ($this->request->getMethod() == 'post') {


            // Validate form data
            $rules = [
                'username' => 'required',
                'password' => 'required'
            ];
            if (!$this->validate($rules)) {
                // Display login form with validation errors
                // return view('home/login', ['validation' => $this->validator]);
                $this->session->setTempdata('error', 'Enter Correct Username and Password', 2);
                return redirect()->to(current_url());
            }

            // Retrieve form data
            $username = $this->request->getVar('username');
            $password = $this->request->getVar('password');

            // Check if user exists

            $user = $this->usersModel->where('username', $username)->first();
            if (!$user) {
                // Display login form with error message
                $this->session->setTempdata('error', 'Incorrect Username', 2);
                return  redirect()->to(current_url());

                //return view('home/login', ['error' => 'Invalid username or password']);
            }

            if (empty($user['orgcode'])) {
                // Display login form with error message
                $this->session->setTempdata('error', 'Username is not associated with any organization', 2);
                return  redirect()->to(current_url());
            }

            // Check if password is correct
            if (!password_verify($password, $user['password'])) {
                // Display login form with error message
                $this->session->setTempdata('error', 'Incorrect Password', 2);
                return redirect()->to(current_url());
                //return view('home/login', ['error' => 'Invalid email or password']);
            }

            $org = $this->orgModel->where('orgcode', $user['orgcode'])->first();

            if ($org['is_active'] != 'active') {
                // Display login form with error message
                $this->session->setTempdata('error', 'Your Organization is not Active. Ask Dakoii Systems to Activate your organization', 2);
                return redirect()->to(previous_url());
            }

            // Store user data in session
            $this->session->set('username', $user['username']);
            $this->session->set('name', $user['name']);
            $this->session->set('role', $user['role']);
            $this->session->set('status', $user['is_active']);
            $this->session->set('orgname', $org['name']);
            $this->session->set('orglogo', $org['orglogo']);
            $this->session->set('orgcode', $org['orgcode']);
            $this->session->set('org_lock_code', $org['loc_code_locked']);
            $this->session->set('org_lock_name', $org['loc_name_locked']);
            $this->session->set('org_lock_level', $org['loc_level_locked']);
            $this->session->set('org_cgps_lon', $org['center_gps_longitude']);
            $this->session->set('org_cgps_lat', $org['center_gps_latitude']);
            $this->session->set('org_cgps_zoom', $org['center_gps_zoom']);
            $this->session->set('is_logged_in', "yes");

            if ($user['is_active'] != 1) {
                $this->session->setTempdata('error', 'Your account is not active', 2);
                return redirect()->to(current_url());
            }

            // Redirect to dashboard
            return redirect()->to('dashboard');
        }


        // Display login form
        //  return view('home/login');

        $data['title'] = "Login";
        $data['menu'] = "login";

        echo view('home/login', $data);
    }


    public function login_po()
    {

        // Check if form has been submitted
        if ($this->request->getMethod() == 'post') {


            // Validate form data
            $rules = [
                'username' => 'required',
                'password' => 'required'
            ];
            if (!$this->validate($rules)) {
                // Display login form with validation errors
                // return view('home/login', ['validation' => $this->validator]);
                $this->session->setTempdata('error', 'Enter Correct Username and Password', 2);
                return redirect()->back();
            }

            // Retrieve form data
            $username = $this->request->getVar('username');
            $password = $this->request->getVar('password');

            // Check if user exists

            $user = $this->project_officersModel->where('username', $username)->first();
            if (!$user) {
                // Display login form with error message
                $this->session->setTempdata('error', 'Incorrect Username', 2);
                return  redirect()->back();

                //return view('home/login', ['error' => 'Invalid username or password']);
            }

            if (empty($user['orgcode'])) {
                // Display login form with error message
                $this->session->setTempdata('error', 'Username is not associated with any organization', 2);
                return  redirect()->back();
            }

            // Check if password is correct
            if (!password_verify($password, $user['password'])) {
                // Display login form with error message
                $this->session->setTempdata('error', 'Incorrect Password', 2);
                redirect()->to(current_url());
                //return view('home/login', ['error' => 'Invalid email or password']);
            }

            $org = $this->orgModel->where('orgcode', $user['orgcode'])->first();

            if ($org['is_active'] != 1) {
                // Display login form with error message
                $this->session->setTempdata('error', 'Your Organization has been Deactivated', 2);
                redirect()->back();
            }

            // Store user data in session
            $this->session->set('userid', $user['id']);
            $this->session->set('username', $user['username']);
            $this->session->set('name', $user['name']);
            $this->session->set('pocode', $user['pocode']);
            $this->session->set('status', $user['status']);
            $this->session->set('orgname', $org['name']);
            $this->session->set('orglogo', $org['orglogo']);
            $this->session->set('orgcode', $org['orgcode']);
            $this->session->set('org_cgps_lon', $org['center_gps_longitude']);
            $this->session->set('org_cgps_lat', $org['center_gps_latitude']);
            $this->session->set('org_cgps_zoom', $org['center_gps_zoom']);
            $this->session->set('is_logged_in', "yes");

            if ($user['status'] != 'active') {
                $this->session->setTempdata('error', 'Your account is not active', 2);
                return redirect()->back();
            }

            // Redirect to dashboard
            return redirect()->to('po_dash');
        }
    }






    public function logout()
    {
        // Destroy the user's session
        $session = session();
        $session->destroy();

        // Redirect to the login page
        return redirect()->to(base_url());
    }
}
