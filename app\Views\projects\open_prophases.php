<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>


<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $pro['name'] ?></h1>
                <h5><?= $pro['procode'] ?></h5>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>project_phases/<?= $pro['procode'] ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Phases </a></li>
                    <li class="breadcrumb-item active"> <?= $pro['name'] ?></li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">
    <div class="row p-2">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info">
                    <a class="btn btn-dark btn-sm" href="<?= base_url() ?>project_phases/<?= $pro['procode'] ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Back </a>
                    <b class=" float-right"><?= $phases['phases'] ?></b>
                </div>
            </div>
        </div>
        <div class=" col-md-4">

            <div class="card">
                <div class="card-header bg-info">
                    <i class="fa fa-plus" aria-hidden="true"></i>
                    Create Milestones </b>
                </div>
                <?= form_open('add_milestones', ['id' => 'add_milestonesForm']) ?>
                <div class="card-body">

                    <div class="row">
                        <!-- <div class="form-group col-md-4">
                           <input type="text" class=" form-control" name="roadcode" id="roadcode" readonly required placeholder="Road Code">
                        </div> -->

                        <div class="form-group col-md-12">
                            <label>Milestone</label>
                            <?= form_input('milestones', set_value('milestones'), ['class' => 'form-control', 'placeholder' => 'Enter Milestone', 'required' => 'required']) ?>
                        </div>
                        <div class="form-group col-md-12">
                            <label>From</label>
                            <input type="date" name="datefrom" class=" form-control" placeholder="From">
                        </div>
                        <div class="form-group col-md-12">
                            <label>To</label>
                            <input type="date" name="dateto" class=" form-control" placeholder="From">
                        </div>

                    </div>

                </div>
                <div class="card-footer">
                    <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                    <input type="hidden" name="phid" value="<?= $phases['id'] ?>">
                    <button type="button" id="add_milestonesBtn" class="btn btn-info float-right"> <i class="fa fa-plus-circle" aria-hidden="true"></i> Add Milestone</button>
                </div>
                <?= form_close() ?>

                <script>
                    $(document).ready(function() {

                        // Add keypress event listener to the form input fields
                        $('#add_milestonesForm input').keypress(function(e) {
                            if (e.which == 13) {
                                e.preventDefault(); // Prevent the default form submission
                                $('#add_milestonesBtn').click(); // Trigger the AJAX function
                            }
                        });


                        $('#add_milestonesBtn').on('click', function() {
                            // Create FormData object to store form data and files
                            var formData = new FormData($('#add_milestonesForm')[0]);

                            // Send an AJAX request
                            $.ajax({
                                url: "<?= base_url('add_milestones'); ?>", // Update this with your controller method
                                type: 'POST',
                                data: formData,
                                contentType: false,
                                processData: false,
                                beforeSend: function() {
                                    // Display a loading indicator
                                    $('#add_milestonesBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Uploading...');
                                },
                                success: function(response) {
                                    // Handle the success response
                                    console.log(response);

                                    // Optionally, display a success message to the user
                                    if (response.status === 'success') {
                                        // Display a success message to the user
                                        toastr.success(response.message);
                                        // Reload page after 1 second
                                        setTimeout(function() {
                                            location.reload();
                                        }, 1000);
                                    } else {
                                        // Display an error message to the user
                                        toastr.error(response.message);

                                        // Reload page after 1 second
                                        setTimeout(function() {
                                            location.reload();
                                        }, 2000);
                                    }

                                },
                                error: function(error) {
                                    // Handle the error response
                                    console.log(error);

                                    // Optionally, display an error message to the user
                                    toastr.error(error);
                                }
                            });
                        });
                    });
                </script>

            </div>

        </div>
        <!-- ./col -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-info">
                    <i class="fas fa-check"></i> Milestones List <b class=" float-right"><?= $phases['phases'] ?></b>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group">
                        <?php foreach ($milestones as $ml) : ?>

                            <!-- li trigger modal -->
                            <li class="list-group-item ">
                                <div class="row">
                                    <div class="col-md-4">
                                        <b><?= $ml['milestones'] ?></b>
                                    </div>
                                    <div class="col-md-4">
                                        <span class=""><b>From:</b><?= dateforms($ml['datefrom']) ?> <b>To:</b> <?= dateforms($ml['dateto']) ?> </span>
                                    </div>
                                    <div class="col-md-4">

                                        <div class="input-group-prepend float-right">
                                            <button type="button" class="btn btn-dark btn-sm dropdown-toggle" data-toggle="dropdown">
                                                Action
                                            </button>
                                            <div class="dropdown-menu">
                                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#edit<?= $ml['id'] ?>">Edit</a>
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item text-danger" href="#" data-toggle="modal" data-target="#del<?= $ml['id'] ?>">Delete</a>
                                            </div>
                                        </div>


                                    </div>

                                </div>

                            </li>

                            <!-- Modal -->
                            <div class="modal fade" id="edit<?= $ml['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                <div class="modal-dialog modal-lg" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header bg-dark">
                                            <h5 class="modal-title"><i class="fa fa-edit"></i> <?= $ml['milestones'] ?></h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <?= form_open('edit_milestones', ['id' => 'edit_milestonesForm' . $ml['id']]) ?>
                                        <div class="modal-body">
                                            <div class="row">

                                                <div class="form-group col-md-12">
                                                    <label>Milestone</label>
                                                    <?= form_input('milestones', $ml['milestones'], ['class' => 'form-control', 'placeholder' => 'Enter Milestone', 'required' => 'required']) ?>
                                                </div>
                                                <div class="form-group col-md-12">
                                                    <label>From</label>
                                                    <input type="date" name="datefrom" class=" form-control" placeholder="From" value="<?= $ml['datefrom'] ?>">
                                                </div>
                                                <div class="form-group col-md-12">
                                                    <label>To</label>
                                                    <input type="date" name="dateto" class=" form-control" placeholder="From" value="<?= $ml['dateto'] ?>">
                                                </div>

                                            </div>

                                        </div>
                                        <div class="modal-footer d-flex justify-content-between">
                                            <span class=" float-left"><small>
                                                    <b>Created: </b><?= datetimeforms($ml['create_at']) ?> | <?= $ml['create_by'] ?><br>
                                                    <b>Updated: </b><?= datetimeforms($ml['update_at']) ?> | <?= $ml['update_by'] ?><br>
                                                </small></span>
                                            <div>
                                                <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                                                <input type="hidden" name="mlid" value="<?= $ml['id'] ?>">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                <button type="button" id="edit_milestonesBtn<?= $ml['id'] ?>" class="btn btn-dark">Save Changes</button>
                                            </div>

                                        </div>
                                        <?= form_close() ?>
                                        <script>
                                            $(document).ready(function() {

                                                // Add keypress event listener to the form input fields
                                                $('#edit_milestonesForm<?= $ml['id'] ?> input').keypress(function(e) {
                                                    if (e.which == 13) {
                                                        e.preventDefault(); // Prevent the default form submission
                                                        $('#edit_milestonesBtn<?= $ml['id'] ?>').click(); // Trigger the AJAX function
                                                    }
                                                });



                                                $('#edit_milestonesBtn<?= $ml['id'] ?>').on('click', function() {
                                                    // Create FormData object to store form data and files
                                                    var formData = new FormData($('#edit_milestonesForm<?= $ml['id'] ?>')[0]);

                                                    // Send an AJAX request
                                                    $.ajax({
                                                        url: "<?= base_url('edit_milestones'); ?>", // Update this with your controller method
                                                        type: 'POST',
                                                        data: formData,
                                                        contentType: false,
                                                        processData: false,
                                                        beforeSend: function() {
                                                            // Display a loading indicator
                                                            $('#edit_milestonesBtn<?= $ml['id'] ?>').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Updating...');
                                                        },
                                                        success: function(response) {
                                                            // Handle the success response
                                                            console.log(response);

                                                            // Optionally, display a success message to the user
                                                            if (response.status === 'success') {
                                                                // Display a success message to the user
                                                                toastr.success(response.message);
                                                                // Reload page after 1 second
                                                                setTimeout(function() {
                                                                    location.reload();
                                                                }, 1000);
                                                            } else {
                                                                // Display an error message to the user
                                                                toastr.error(response.message);

                                                                // Reload page after 1 second
                                                                setTimeout(function() {
                                                                    location.reload();
                                                                }, 2000);
                                                            }

                                                        },
                                                        error: function(error) {
                                                            // Handle the error response
                                                            console.log(error);

                                                            // Optionally, display an error message to the user
                                                            toastr.error(error);
                                                        }
                                                    });
                                                });
                                            });
                                        </script>
                                    </div>

                                </div>

                            </div>

                            <!-- Modal -->
                            <div class="modal fade" id="del<?= $ml['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                <div class="modal-dialog modal-lg" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger">
                                            <h5 class="modal-title"><i class="fa fa-exclamation-triangle"></i> You want to Delete </h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <?= form_open('delete_milestones', ['id' => 'delete_milestonesForm' . $ml['id']]) ?>
                                        <div class="modal-body">
                                            <div class="row">

                                                <div class="form-group col-md-12">
                                                    <label><?= $ml['milestones'] ?></label>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="modal-footer ">
                                            <div>
                                                <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                                                <input type="hidden" name="mlid" value="<?= $ml['id'] ?>">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                <button type="button" id="delete_milestonesBtn<?= $ml['id'] ?>" class="btn btn-danger float-right "> <i class="fas fa-trash-alt    "></i> Delete </button>
                                            </div>

                                        </div>
                                        <?= form_close() ?>
                                        <script>
                                            $(document).ready(function() {

                                                // Add keypress event listener to the form input fields
                                                $('#delete_milestonesForm<?= $ml['id'] ?> input').keypress(function(e) {
                                                    if (e.which == 13) {
                                                        e.preventDefault(); // Prevent the default form submission
                                                        $('#delete_milestonesBtn<?= $ml['id'] ?>').click(); // Trigger the AJAX function
                                                    }
                                                });


                                                $('#delete_milestonesBtn<?= $ml['id'] ?>').on('click', function() {
                                                    // Create FormData object to store form data and files
                                                    var formData = new FormData($('#delete_milestonesForm<?= $ml['id'] ?>')[0]);

                                                    // Send an AJAX request
                                                    $.ajax({
                                                        url: "<?= base_url('delete_milestones'); ?>", // Update this with your controller method
                                                        type: 'POST',
                                                        data: formData,
                                                        contentType: false,
                                                        processData: false,
                                                        beforeSend: function() {
                                                            // Display a loading indicator
                                                            $('#delete_milestonesBtn<?= $ml['id'] ?>').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Deleting...');
                                                        },
                                                        success: function(response) {
                                                            // Handle the success response
                                                            console.log(response);

                                                            // Optionally, display a success message to the user
                                                            if (response.status === 'success') {
                                                                // Display a success message to the user
                                                                toastr.success(response.message);
                                                                // Reload page after 1 second
                                                                setTimeout(function() {
                                                                    location.reload();
                                                                }, 1000);
                                                            } else {
                                                                // Display an error message to the user
                                                                toastr.error(response.message);

                                                                // Reload page after 1 second
                                                                setTimeout(function() {
                                                                    location.reload();
                                                                }, 2000);
                                                            }

                                                        },
                                                        error: function(error) {
                                                            // Handle the error response
                                                            console.log(error);

                                                            // Optionally, display an error message to the user
                                                            toastr.error(error);
                                                        }
                                                    });
                                                });
                                            });
                                        </script>
                                    </div>

                                </div>


                            </div>
                            <!-- End of Modal -->

                        <?php endforeach; ?>

                    </ul>
                </div>

            </div>
        </div>
    </div>
</section>


</body>



<?= $this->endSection() ?>