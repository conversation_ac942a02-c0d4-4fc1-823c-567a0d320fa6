<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<!-- DataTables -->
<link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
<link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">

<!-- links -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@2.9.3/dist/Chart.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.3/html2pdf.bundle.min.js"></script>

<!-- DataTables  & Plugins -->
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables/jquery.dataTables.min.js"></script>
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/jszip/jszip.min.js"></script>
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/pdfmake/pdfmake.min.js"></script>
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/pdfmake/vfs_fonts.js"></script>
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/js/buttons.colVis.min.js"></script>



<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= ucfirst($status) ?> Projects</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item active"><?= ucfirst($status) ?> Projects</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<div class="container-fluid" id="printpdf">



    <div class="row">
        <div class="col-md-12">
            <div class="card bg-dark ">
                <!--tips: add .text-center,.text-right to the .card to change card text alignment-->
                <div class="card-header p-1">
                    <a href="<?= base_url() ?>dashboard" class=" btn btn-light text-dark"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Go Back</a>
                    <span class=" float-right btn btn-dark"><?= ucfirst($status) ?> Projects Dashboard</span>
                </div>

            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <h5 class="float-left">Summary Report</h5>
            <div class="float-right"><?= session('orgname') ?></div>

        </div>
    </div>
    <hr>

    <div class="row">
        <div class="col-md-12">
            <!-- Info boxes -->
            <div class="row">

                <div class="col-md-12">
                    <span>Payments</span>
                    <div class="card card-outline card-info">

                        <div class="card-body p-0">
                            <table class="table">
                                <tbody>
                                    <tr class=" text-bold">
                                        <td scope="row">T.Budgeted</td>
                                        <td>T.Paid</td>
                                        <td>T.Outstanding</td>
                                        <td>T.OverPaid</td>
                                    </tr>
                                    <tr>
                                        <td scope="row"><?= COUNTRY_CURRENCY . number_format($pro_total_budget, 2) ?></td>
                                        <td scope="row"><?= COUNTRY_CURRENCY . number_format($pro_total_paid, 2) ?></td>
                                        <td scope="row"><?= COUNTRY_CURRENCY . number_format($pro_total_outstanding, 2) ?></td>
                                        <td scope="row"><?= COUNTRY_CURRENCY . number_format($pro_total_overpaid, 2) ?></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.row -->

            <div class="row mb-0">
                <div class="col-md-12">
                    <span>Milestones</span>

                    <div class="row mt-0">
                        <div class="col-md-3">
                            <div class="callout callout-info">
                                <span class="text-left "><i class="fa fa-hourglass-1" aria-hidden="true"></i> T. MS. PENDING</span>
                                <span class=" badge badge-dark float-right"> <?= ($pro_ms_pending) ?> </span>
                            </div>
                        </div>
                        <!-- /.col -->

                        <div class="col-md-3">
                            <div class="callout callout-info">
                                <span class="text-left "><i class="fa fa-check-circle" aria-hidden="true"></i> T. MS. COMPLETED</span>
                                <span class=" badge badge-dark float-right"> <?= ($pro_ms_completed) ?> </span>
                            </div>
                        </div>
                        <!-- /.col -->

                        <div class="col-md-3">
                            <div class="callout callout-info">
                                <span class="text-left "><i class="fa fa-exclamation-circle" aria-hidden="true"></i> T. MS. HOLD</span>
                                <span class=" badge badge-dark float-right"> <?= ($pro_ms_hold) ?> </span>
                            </div>
                        </div>
                        <!-- /.col -->

                        <div class="col-md-3">
                            <div class="callout callout-info">
                                <span class="text-left "><i class="fa fa-clipboard-list" aria-hidden="true"></i> T. MS. Milestones</span>
                                <span class=" badge badge-dark float-right"> <?= count($milestones) ?> </span>
                            </div>
                        </div>
                        <!-- /.col -->

                    </div>

                </div>
            </div>
            <!-- /.row -->

        </div>
        <!-- /.col -->
    </div>
    <!-- /.row -->

    <div class="row">
        <!-- OTHER DATA -->
        <div class="col-md-12">

            <div class="card card-info" id="projects_list">
                <div class="card-header">
                    <span><?= ucfirst($status) ?> Projects List</span>
                    <!-- <button onclick="printCard('projects_list')" class=" btn btn-dark btn-sm float-right d-print-none"> <i class="fa fa-print" aria-hidden="true"></i> </button>
                    <button onclick="printTable('projects_table','Projects List | <?= session('orgname') ?>')" class=" btn btn-default btn-sm float-right d-print-none"> <i class="fa fa-print" aria-hidden="true"></i> </button>
                    <button onclick="copyToImageHD('projects_list')" class=" btn btn-default btn-sm float-right d-print-none"> <i class="fa fa-copy" aria-hidden="true"></i> </button> -->
                </div>
                <div class="card-body p-0 table-responsive">
                    <table class="table " id="projects_table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Code</th>
                                <th>Project</th>
                                <th>P.Date</th>
                                <th>Fund</th>
                                <th>Budget (<?= COUNTRY_CURRENCY ?>)</th>
                                <th>T.Paid (<?= COUNTRY_CURRENCY ?>)</th>
                                <th>Out.P (<?= COUNTRY_CURRENCY ?>)</th>
                                <th>Over.P (<?= COUNTRY_CURRENCY ?>)</th>
                                <th>Contractor</th>
                                <th>P.Officer</th>
                                <th>MS(P,H,C/T)</th>
                                <th>Status</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $x = 1;
                            foreach ($projects as $pro) : ?>

                                <?php
                                $ms_pending = $ms_hold = $ms_completed = $ms_total = 0;
                                foreach ($milestones as $ms) {
                                    if ($ms['procode'] == $pro['procode']) {
                                        if ($ms['checked'] == "pending") {

                                            $ms_pending++;
                                        }
                                        if ($ms['checked'] == 'hold') {
                                            $ms_hold++;
                                        }
                                        if ($ms['checked'] == 'completed') {
                                            $ms_completed++;
                                        }
                                    }
                                }
                                $ms_total = $ms_pending + $ms_hold + $ms_completed;
                                ?>

                                <tr>
                                    <td><?= $x++ ?></td>
                                    <td><?= $pro['procode'] ?></td>
                                    <td><?= $pro['name'] ?></td>
                                    <td><?= dateforms($pro['pro_date']) ?></td>
                                    <td><?= strtoupper($pro['fund']) ?></td>
                                    <td>
                                        <?= number_format(checkZero($pro['budget']), 2) ?></td>
                                    <td><?= number_format(checkZero($pro['payment_total']), 2) ?></td>
                                    <td>
                                        <?php $t_outstanding = checkZero($pro['budget']) - checkZero($pro['payment_total']);
                                        if($t_outstanding < 0){
                                            $t_outstanding = 0;
                                        }
                                        ?>
                                        <?= number_format(checkZero($t_outstanding), 2) ?>
                                    </td>
                                    <td>
                                        <?php 
                                        $t_overpay = checkZero($pro['payment_total']) - checkZero($pro['budget']);
                                        if($t_overpay < 0){
                                            $t_overpay = 0;
                                        }
                                        ?>
                                        <?= number_format(checkZero($t_overpay), 2) ?>
                                    </td>
                                    <td><?= $pro['contractor_name'] ?></td>
                                    <td><?= $pro['pro_officer_name'] ?></td>
                                    <td>


                                        <?= $ms_pending ?> , <?= $ms_hold ?> , <?= $ms_completed ?> / <?= $ms_total ?>
                                    </td>
                                    <td><?= ucfirst($pro['status']) ?></td>
                                    <td><a class="btn btn-primary btn-sm float-right" href="<?= base_url() ?>report_projects_view/<?= $pro['ucode'] ?>">View Report</a></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <!-- /.card-body -->
                <div class="card-footer">
                    <small><b>Org: </b><?= session('orgname') ?></small>
                </div>
            </div>
            <!-- /.card -->


        </div>
        <!-- ./ col  -->

    </div>
    <!-- /.row -->
    <script>
        $(function() {
            $("#projects_table").DataTable({
                "responsive": false,
                "lengthChange": false,
                "autoWidth": false,
                "buttons": [{
                        extend: 'excel',
                        text: 'Excel',
                        filename: '<?= $title ?>', // Set custom filename
                        exportOptions: {
                            modifier: {
                                page: 'all'
                            },
                            title: '<?= $title ?>'
                        }
                    },
                    {
                        extend: 'colvis',
                        text: 'View Columns',

                    }
                ],
                "columnDefs": [{
                    "targets": "_all",
                    "className": "text-nowrap"
                }]
                //"buttons": ["copy", "excel", "print", "colvis"]
            }).buttons().container().appendTo('#projects_table_wrapper .col-md-6:eq(0)');
            $('#example2').DataTable({
                "paging": true,
                "lengthChange": false,
                "searching": false,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
            });
        });
    </script>

    <script>
        /*    $(document).ready(function() {

            // DataTable
            var table = $('#datatble').DataTable({
                // data
            });

            // PDF button
            $('#datatble').DataTable({
                dom: 'Bfrtip',
                buttons: [
                    'pdf'
                ]
            });

        }); */
    </script>


</div>




<?= $this->endSection(); ?>