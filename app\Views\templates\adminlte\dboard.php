<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><?= $title ?></title>

  <link rel="shortcut icon" type="image/x-icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico">


  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
 <!--  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/fontawesome-free/css/all.min.css"> -->
  <!-- fullCalendar -->
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/fullcalendar/main.css">
  <!-- Toastr -->
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/toastr/toastr.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/dist/css/adminlte.min.css">
  
  <!-- Ionicons -->
  <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">

  <!-- jQuery -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/jquery/jquery.min.js"></script>
  <!-- Bootstrap -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/bootstrap/js/bootstrap.bundle.min.js">
  </script>
  <!-- Toastr -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/toastr/toastr.min.js"></script>
  <!-- jQuery UI -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/jquery-ui/jquery-ui.min.js"></script>
  <!-- bs-custom-file-input -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
  <!-- AdminLTE App -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/dist/js/adminlte.min.js"></script>
  <!-- fullCalendar 2.2.5 -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/moment/moment.min.js"></script>

  <!-- AdminLTE for demo purposes -->
  <!-- <script src="<?= base_url() ?>/public/assets/themes/adminlte320/dist/js/demo.js"></script> -->
  <!-- Page specific script -->

  <script>
    $(function() {
      bsCustomFileInput.init();
    });
  </script>

</head>

<body class="hold-transition sidebar-mini">
  <div class="wrapper">

    <?php if (session()->has('error')) : ?>
      <span class="toastrDefaultError"></span>
    <?php endif; ?>
    <?php if (session()->has('success')) : ?>
      <span class="toastrDefaultSuccess"></span>
    <?php endif; ?>

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
      <!-- Left navbar links -->
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
        <li class="nav-item d-none d-sm-inline-block">
          <a href="<?= base_url() ?>dashboard" class="nav-link"><i class=" nav-icon fa fa-tachometer-alt" aria-hidden="true"></i></a>
        </li>

      </ul>

      <!-- Right navbar links -->
      <ul class="navbar-nav ml-auto">


        <li class="nav-item">
          <a class="nav-link" data-widget="fullscreen" href="#" role="button">
            <i class="fas fa-expand-arrows-alt"></i>
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" data-widget="control-sidebar" data-slide="true" href="#" role="button">
            <i class="fas fa-th-large"></i>
          </a>
        </li>
      </ul>
    </nav>
    <!-- /.navbar -->

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
      <!-- Brand Logo -->
      <a href="#" class="brand-link">
      <img src="<?= imgcheck(session('orglogo')) ?>" alt="org logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light"> <?= session('orgname') ?> <small > <?= session('orgcode') ?></small> </span>
        
      </a>

      <!-- Sidebar -->
      <div class="sidebar">
        <!-- Sidebar user panel (optional) -->
        <div class="user-panel mt-3 pb-3 mb-3 d-flex">
          <div class="image">
            <img src="<?= base_url() ?>/public/assets/system_img/no-users-img.png" class="img-circle elevation-2" alt="User Image">
          </div>
          <div class="info">
            <a href="#" class="d-block"><?= session('name') ?></a>
          </div>
        </div>

        <!-- Sidebar Menu -->
        <nav class="mt-2">
          <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
            <!-- Add icons to the links using the .nav-icon class
               with font-awesome or any other icon font library -->
            <li class="nav-item">

              <?php $active = "";
              if ($menu == "dashboard") {
                $active = "active";
              } ?>
              <a href="<?= base_url() ?>dashboard" class="nav-link <?= $active ?>">
                <i class=" nav-icon fa fa-tachometer-alt" aria-hidden="true"></i>
                <p>
                  Dashboard
                </p>
              </a>

            </li>
            <li class="nav-item">
              <?php
              $active = "";
              if ($menu == "projects") {
                $active = "active";
              } ?>
              <a href="<?= base_url() ?>projects" class="nav-link <?= $active ?>">
                <i class=" nav-icon fa fa-list-alt" aria-hidden="true"></i>
                <p>
                  Projects List
                </p>
              </a>
            </li>
            <li class="nav-item">
              <?php $active = "";
              if ($menu == "addprojects") {
                $active = "active";
              } ?>
              <a href="<?= base_url() ?>new_projects" class="nav-link <?= $active ?>">
                <i class=" nav-icon fas fa-plus-circle" aria-hidden="true"></i>
                <p>
                  New Project
                </p>
              </a>

            </li>
            
            <li class="nav-item">
              <?php
              $active = "";
              if ($menu == "contractors") {
                $active = "active";
              } ?>
              <a href="<?= base_url() ?>contractors" class="nav-link <?= $active ?>">
                <i class=" nav-icon fas fa-toolbox" aria-hidden="true"></i>
                <p>
                  Contractors
                </p>
              </a>
            </li>
            
            <li class="nav-item">
              <?php
              $active = "";
              if ($menu == "new_contractors") {
                $active = "active";
              } ?>
              <a href="<?= base_url() ?>contractors_new" class="nav-link <?= $active ?>">
                <i class=" nav-icon fas fa-plus-circle" aria-hidden="true"></i>
                <p>
                  New Contractor
                </p>
              </a>
            </li>
            
            <li class="nav-item">
              <?php
              $active = "";
              if ($menu == "project_officers") {
                $active = "active";
              } ?>
              <a href="<?= base_url() ?>project_officers" class="nav-link <?= $active ?>">
                <i class=" nav-icon fas fa-user-lock" aria-hidden="true"></i>
                <p>
                  Project Officers
                </p>
              </a>
            </li>

            <li class="nav-item">

              <a href="<?= base_url() ?>logout" class="nav-link bg-danger">
                <i class="fa fa-sign-out-alt" aria-hidden="true"></i>
                <p>
                  Log Out (<?= session('username') ?>)
                </p>
              </a>
            </li>

          </ul>
        </nav>
        <!-- /.sidebar-menu -->
      </div>
      <!-- /.sidebar -->
    </aside>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
      <?= $this->renderSection('content') ?>
    </div>
    <!-- /.content-wrapper -->

    <footer class="main-footer">
      <div class="float-right d-none d-sm-block">
        <b><?= SYSTEM_NAME ?></b>
      </div>
      <strong>Copyright &copy; 2023 <a href="https://www.dakoiims.com">Dakoii Systems</a>.</strong> All rights
      reserved.
    </footer>

    <!-- Control Sidebar -->
    <aside class="control-sidebar control-sidebar-dark">
      <!-- Control sidebar content goes here -->
    </aside>
    <!-- /.control-sidebar -->
  </div>
  <!-- ./wrapper -->


  <?= $this->renderSection('calendar'); ?>
</body>

<script>
  $('.toastrDefaultSuccess').show(function() {
    toastr.success('<?= session('success') ?>')
   // toastr.success('Cook Liks')
  });
  $('.toastrDefaultError').show(function() {
    toastr.error('<?= session('error') ?>')
  });
</script>


</html>