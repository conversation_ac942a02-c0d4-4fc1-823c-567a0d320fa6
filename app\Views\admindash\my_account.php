<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>


<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">My Account</h1>
                <h5><?= $myacc['name'] ?></h5>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Dashboard</a></li>
                    <li class="breadcrumb-item active">My Account</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<div class="container-fluid mt-1">

    <div class="row">
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-primary">
                    <i class="fa fa-info-circle" aria-hidden="true"></i>
                    <span>My Account Information</span>
                </div>
                <?= form_open('update_admin_orginfo') ?>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-md-3">
                            <label for="my-input">Code</label>
                            <input id="my-input" class="form-control" type="text" name="orgcode" value="<?= $myacc['orgcode'] ?>" disabled>
                        </div>
                        <div class="form-group col-md-9">
                            <label for="my-input">Name</label>
                            <input id="my-input" class="form-control" type="text" name="name" value="<?= $myacc['name'] ?>">
                        </div>
                        <div class="form-group col-md-12">
                            <label for="my-input">Brief Description</label>
                            <textarea name="description" id="" cols="30" rows="5" name="description" class="form-control"><?= $myacc['description'] ?></textarea>
                        </div>

                        <label class=" col-md-12 text-muted "> Location</label>
                        <div class="form-group col-md-3">
                            <select name="country" id="country" class="form-control">
                                <option selected value="<?= $set_country['code'] ?>"><?= $set_country['name'] ?></option>
                            </select>

                        </div>
                        <div class="form-group col-md-3">
                            <select name="province" id="province" class="form-control">
                                <option value="">Select Province</option>
                                <?php foreach ($get_provinces as $prov) :
                                    if ($myacc['province_code'] == $prov['provincecode']) {
                                ?>
                                        <option selected value="<?= $prov['provincecode'] ?>"><?= $prov['name'] ?></option>
                                    <?php
                                    } else {
                                    ?>
                                        <option value="<?= $prov['provincecode'] ?>"><?= $prov['name'] ?></option>
                                <?php }
                                endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <select name="district" id="district" class="form-control">
                                <option selected value="<?= $myacc['district_code'] ?>"><?= $get_district['name'] ?></option>
                            </select>
                        </div>

                        <div class="form-group col-md-3">
                            <select name="llg" id="llg" class="form-control">
                                <option selected value="<?= $myacc['llg_code'] ?>"><?= $get_llg['name'] ?></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <input type="hidden" name="id" value="<?= $myacc['id'] ?>">
                    <button type="submit" class="btn btn-primary float-right"> <i class="fas fa-save    "></i> Save Changes </button>
                </div>
                <?= form_close() ?>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-header bg-primary">
                    <i class="fas fa-image    "></i>
                    <span>Logo</span>
                    <!-- Button trigger modal -->
                    <button type="button" class="btn btn-dark float-right" data-toggle="modal" data-target="#edit_logo">
                        <i class="fas fa-pen    "></i> Set Logo
                    </button>

                    <!-- Modal -->
                    <div class="modal fade" id="edit_logo" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                        <div class="modal-dialog modal-lg" role="document">
                            <div class="modal-content">
                                <div class="modal-header bg-primary">
                                    <h5 class="modal-title"> <i class="fas fa-edit    "></i> Change Logo </h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <?= form_open_multipart('update_admin_orglogo') ?>
                                <div class="modal-body">
                                    <div class="form-group col-md-12">
                                        <div class="input-group ">
                                            <div class="custom-file ">
                                                <input type="file" class="custom-file-input " name="logo_file" id="exampleInputFile" accept="image/*" required>
                                                <label class="custom-file-label " for="exampleInputFile">Logo File</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <input type="hidden" name="id" value="<?= $myacc['id'] ?>">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                    <button type="submit" class="btn btn-primary"> <i class="fa fa-upload" aria-hidden="true"></i> Upload Logo</button>
                                </div>
                                <?= form_close() ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <img src="<?= imgcheck($myacc['orglogo']) ?>" class=" card-img" alt="">
                </div>
            </div>
        </div>

    </div>
    <!-- /.row -->

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary">
                    <i class="fas fa-user-shield    "></i>
                    Administrators
                </div>
                <div class="card-body p-0">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Name</th>
                                <th>Username</th>
                                <th>Role</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $x=1; foreach ($admins as $ad) : ?>
                                <tr>
                                    <td scope="row"><?= $x++ ?></td>
                                    <td><?= $ad['name'] ?></td>
                                    <td><?= $ad['username'] ?></td>
                                    <td><?= $ad['role'] ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- ./ col - dashboard -->

<script>
    $(document).ready(function() {
        $('#province').change(function() {
            var province_code = $(this).val();

            $.ajax({
                url: '<?= base_url() ?>getaddress',
                type: 'post',
                data: {
                    province_code: province_code
                },
                dataType: 'json',
                success: function(response) {
                    var len = response.district.length;

                    $("#district").empty();
                    $("#district").append("<option value=''>Select a District</option>");

                    for (var i = 0; i < len; i++) {
                        var code = response.district[i]['districtcode'];
                        var name = response.district[i]['name'];
                        //var code = response.subcategories[i]['code'];

                        $("#district").append("<option value='" + code + "'>" + name +
                            "</option>");

                    }
                }
            });
        });



        $('#district').change(function() {
            var district_code = $(this).val();

            $.ajax({
                url: '<?= base_url() ?>getaddress',
                type: 'post',
                data: {
                    district_code: district_code
                },
                dataType: 'json',
                success: function(response) {
                    console.log(response);
                    var len = response.llgs.length;
                    $("#llg").empty();
                    $("#llg").append("<option value=''>Select a LLG</option>");
                    for (var i = 0; i < len; i++) {

                        var code = response.llgs[i]['llgcode'];
                        var name = response.llgs[i]['name'];

                        $("#llg").append("<option value='" + code + "'>" + name + "</option>");
                    }
                }
            });
        });

    });
</script>



<?= $this->endSection(); ?>