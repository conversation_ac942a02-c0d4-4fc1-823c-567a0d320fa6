<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">

            <div class="col-sm-6">
                <h1 class="m-0">Register New Contractor</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>contractors"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Contract List</a></li>
                    <li class="breadcrumb-item active">New Contractor</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->


<section class=" container-fluid">

    <?= form_open('create_contractor') ?>
    <div class="row p-2">
        <div class=" col-md-12">
            <div class="card">
                <div class="card-header bg-primary">
                    <i class="fa fa-bookmark" aria-hidden="true"></i> Contractor Details
                </div>

                <div class="card-body">

                    <div class="row">
                        <!-- <div class="form-group col-md-4">
                           <input type="text" class=" form-control" name="roadcode" id="roadcode" readonly required placeholder="Road Code">
                        </div> -->

                        <div class="form-group col-md-12">
                            <?= form_input('name', set_value('name'), ['class' => 'form-control', 'placeholder' => 'Contractor Name', 'required' => 'required']) ?>
                        </div>

                        <div class="form-group col-md-12">
                            <select class="form-control" name="category" class="form-control" required>
                                <option value="">Select Category</option>
                                <?php foreach ($con_cat as $cc) : ?>
                                    <option value="<?= $cc['value'] ?>"><?= $cc['item'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group col-md-12">
                            <?= form_textarea('services', set_value('services'), ['class' => 'form-control', 'placeholder' => 'List all the services provided by this contractor']) ?>
                            <small class=" text-muted"><i class="fa fa-info-circle" aria-hidden="true"></i> List each service on each line</small>
                        </div>

                        <label class=" col-md-12 text-muted "> Contractor Location</label>
                        <div class="form-group col-md-3">
                            <select name="country" id="country" class="form-control" required>
                                <option selected value="<?= $set_country['code'] ?>"><?= $set_country['name'] ?></option>
                            </select>

                        </div>
                        <div class="form-group col-md-3">
                            <select name="province" id="province" class="form-control" required> 
                                <option value="">Select Province</option>
                                <?php foreach ($get_provinces as $prov) : ?>
                                    <option value="<?= $prov['provincecode'] ?>"><?= $prov['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <select name="district" id="district" class="form-control" required>
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <select name="llg" id="llg" class="form-control" required>
                            </select>
                        </div>

                    </div>

                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary float-right shadow">REGISTER NEW CONTRACTOR</button>
                        </div>
                    </div>
                    <!--  ./row -->
                </div>

            </div>

        </div>
        <!-- ./ col -->

    </div>
    <!-- ./ row -->

    <?= form_close() ?>

</section>

</body>

<script>
    $(document).ready(function() {
        $('#province').change(function() {
            var province_code = $(this).val();

            $.ajax({
                url: '<?= base_url() ?>getaddress',
                type: 'post',
                data: {
                    province_code: province_code
                },
                dataType: 'json',
                success: function(response) {
                    var len = response.district.length;

                    $("#district").empty();
                    $("#district").append("<option value=''>Select a District</option>");

                    for (var i = 0; i < len; i++) {
                        var code = response.district[i]['districtcode'];
                        var name = response.district[i]['name'];
                        //var code = response.subcategories[i]['code'];

                        $("#district").append("<option value='" + code + "'>" + name +
                            "</option>");

                    }
                }
            });
        });



        $('#district').change(function() {
            var district_code = $(this).val();

            $.ajax({
                url: '<?= base_url() ?>getaddress',
                type: 'post',
                data: {
                    district_code: district_code
                },
                dataType: 'json',
                success: function(response) {
                    console.log(response);
                    var len = response.llgs.length;
                    $("#llg").empty();
                    $("#llg").append("<option value=''>Select a LLG</option>");
                    for (var i = 0; i < len; i++) {

                        var code = response.llgs[i]['llgcode'];
                        var name = response.llgs[i]['name'];

                        $("#llg").append("<option value='" + code + "'>" + name + "</option>");
                    }
                }
            });
        });

    });
</script>



<?= $this->endSection() ?>