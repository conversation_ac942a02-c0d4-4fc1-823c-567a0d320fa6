<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>


<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $pro['name'] ?></h1>
                <h5><?= $pro['procode'] ?></h5>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>open_projects/<?= $pro['ucode'] ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Open Projects </a></li>
                    <li class="breadcrumb-item active"> <?= $pro['name'] ?></li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">

    <div class="row">
        <!-- ./col -->
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info ">
                    <a class="btn btn-dark btn-sm" href="<?= base_url() ?>open_projects/<?= $pro['ucode'] ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Back </a>
                    Phases and Milestones
                    <!-- Button trigger modal -->
                    <button type="button" class="btn btn-sm btn-dark float-right" data-toggle="modal" data-target="#addphases">
                        <i class="fa fa-plus-circle" aria-hidden="true"></i> Create Phase
                    </button>

                    <!-- Modal -->
                    <div class="modal fade" id="addphases" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                        <div class="modal-dialog modal-lg" role="document">
                            <div class="modal-content">
                                <div class="modal-header bg-dark ">
                                    <h5 class="modal-title"> <i class="fa fa-plus-circle" aria-hidden="true"></i> Create Phase</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <?= form_open('add_phases', ['id' => 'add_phasesForm']) ?>
                                <div class="modal-body">
                                    <div class="input-group">
                                        <div class="custom-file">
                                            <?= form_input('phases', set_value('phases'), ['class' => 'form-control form-control-border', 'placeholder' => 'Enter Phase Title', 'required' => 'required']) ?>
                                            </label>
                                        </div>
                                        <div class="input-group-append">
                                            <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                                            <button type="button" id="btnAddPhases" class="btn btn-dark float-right">Add Phase</button>
                                        </div>
                                    </div>
                                </div>
                                <?= form_close() ?>

                                <script>
                                    $(document).ready(function() {

                                        // Add keypress event listener to the form input fields
                                        $('#add_phasesForm input').keypress(function(e) {
                                            if (e.which == 13) {
                                                e.preventDefault(); // Prevent the default form submission
                                                $('#btnAddPhases').click(); // Trigger the AJAX function
                                            }
                                        });


                                        $('#btnAddPhases').on('click', function() {
                                            // Create FormData object to store form data and files
                                            var formData = new FormData($('#add_phasesForm')[0]);

                                            // Send an AJAX request
                                            $.ajax({
                                                url: "<?= base_url('add_phases'); ?>", // Update this with your controller method
                                                type: 'POST',
                                                data: formData,
                                                contentType: false,
                                                processData: false,
                                                beforeSend: function() {
                                                    // Display a loading indicator
                                                    $('#btnAddPhases').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Uploading...');
                                                },
                                                success: function(response) {
                                                    // Handle the success response
                                                    console.log(response);

                                                    // Optionally, display a success message to the user
                                                    if (response.status === 'success') {
                                                        // Display a success message to the user
                                                        toastr.success(response.message);

                                                        // Reload page after 1 second
                                                        setTimeout(function() {
                                                            location.reload();
                                                        }, 1000);
                                                    } else {
                                                        // Display an error message to the user
                                                        toastr.error(response.message);

                                                        // Reload page after 1 second
                                                        setTimeout(function() {
                                                            location.reload();
                                                        }, 2000);
                                                    }

                                                },
                                                error: function(error) {
                                                    // Handle the error response
                                                    console.log(error.responseText);

                                                    // Optionally, display an error message to the user
                                                    toastr.error(response.message);
                                                }
                                            });
                                        });
                                    });
                                </script>

                            </div>
                        </div>
                    </div>
                    <!-- ./ modal -->
                </div>
                <div class="card-body p-0">
                    <ul class="list-group">
                        <?php foreach ($phases as $ph) : ?>
                            <li class="list-group-item text-dark ">

                                <div class="font-weight-bolder float-left">
                                    Phase:
                                    <a class="" href="<?= base_url() ?>open_prophases/<?= $ph['ucode'] ?>">
                                        <strong class=" "> <i class="fa fa-circle" aria-hidden="true"></i> <?= $ph['phases'] ?></strong>
                                    </a>
                                </div>

                                <div class="input-group-prepend float-right">
                                    <button type="button" class="btn btn-dark btn-sm dropdown-toggle" data-toggle="dropdown">
                                        Action
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="<?= base_url() ?>open_prophases/<?= $ph['ucode'] ?>">View</a>
                                        <a class="dropdown-item" href="#" data-toggle="modal" data-target="#edit<?= $ph['id'] ?>">Edit</a>
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item text-danger" href="#" data-toggle="modal" data-target="#delete<?= $ph['id'] ?>">Delete</a>
                                    </div>
                                </div>

                                <!-- Modal -->
                                <div class="modal fade" id="edit<?= $ph['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header bg-dark">
                                                <h5 class="modal-title"><i class="fas fa-edit    "></i> Edit Phase</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <?= form_open('edit_phases', ['id' => 'edit_phasesForm' . $ph['id']]) ?>
                                            <div class="modal-body">
                                                <div class="input-group">
                                                    <div class="custom-file">
                                                        <?= form_input('phases', $ph['phases'], ['class' => 'form-control form-control-border', 'placeholder' => 'Enter Phase Title', 'required' => 'required']) ?>
                                                        </label>
                                                    </div>
                                                    <div class="input-group-append">
                                                        <input type="hidden" name="phid" value="<?= $ph['id'] ?>">
                                                        <button type="button" id="edit_phasesBtn<?= $ph['id'] ?>" class="btn btn-dark float-right">Save Changes</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <?= form_close() ?>

                                            <script>
                                                $(document).ready(function() {

                                                    // Add keypress event listener to the form input fields
                                                    $('#edit_phasesForm<?= $ph['id'] ?>').keypress(function(e) {
                                                        if (e.which == 13) {
                                                            e.preventDefault(); // Prevent the default form submission
                                                            $('#edit_phasesBtn<?= $ph['id'] ?>').click(); // Trigger the AJAX function
                                                        }
                                                    });


                                                    $('#edit_phasesBtn<?= $ph['id'] ?>').on('click', function() {
                                                        // Create FormData object to store form data and files
                                                        var formData = new FormData($('#edit_phasesForm<?= $ph['id'] ?>')[0]);

                                                        // Send an AJAX request
                                                        $.ajax({
                                                            url: "<?= base_url('edit_phases'); ?>", // Update this with your controller method
                                                            type: 'POST',
                                                            data: formData,
                                                            contentType: false,
                                                            processData: false,
                                                            beforeSend: function() {
                                                                // Display a loading indicator
                                                                $('#edit_phasesBtn<?= $ph['id'] ?>').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Uploading...');
                                                            },
                                                            success: function(response) {
                                                                // Handle the success response
                                                                console.log(response);

                                                                // Optionally, display a success message to the user
                                                                toastr.success(response.message);

                                                                // Reload page after 1 seconds
                                                                setTimeout(function() {
                                                                    location.reload();
                                                                }, 1000);
                                                            },
                                                            error: function(error) {
                                                                // Handle the error response
                                                                console.log(error.responseText);

                                                                // Optionally, display an error message to the user
                                                                toastr.error(response.message);
                                                            }
                                                        });
                                                    });
                                                });
                                            </script>

                                        </div>
                                    </div>
                                </div>

                                <!-- Modal -->
                                <div class="modal fade" id="delete<?= $ph['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header bg-danger">
                                                <h5 class="modal-title"><i class="fas fa-exclamation-triangle"></i> You want to Delete </h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <?= form_open('delete_phases', ['id' => 'delete_phasesForm' . $ph['id']]) ?>
                                            <div class="modal-body">
                                                <div class="input-group">
                                                    <div class="custom-file">
                                                        <label><?= $ph['phases'] ?></label>
                                                    </div>
                                                    <div class="input-group-append">
                                                        <input type="hidden" name="phid" value="<?= $ph['id'] ?>">
                                                        <button type="button" id="delete_phasesBtn<?= $ph['id'] ?>" class="btn btn-danger float-right"> <i class="fas fa-trash-alt    "></i> Delete </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <?= form_close() ?>

                                            <script>
                                                $(document).ready(function() {


                                                    // Add keypress event listener to the form input fields
                                                    $('#delete_phasesForm<?= $ph['id'] ?> input').keypress(function(e) {
                                                        if (e.which == 13) {
                                                            e.preventDefault(); // Prevent the default form submission
                                                            $('#delete_phasesBtn<?= $ph['id'] ?>').click(); // Trigger the AJAX function
                                                        }
                                                    });


                                                    $('#delete_phasesBtn<?= $ph['id'] ?>').on('click', function() {
                                                        // Create FormData object to store form data and files
                                                        var formData = new FormData($('#delete_phasesForm<?= $ph['id'] ?>')[0]);

                                                        // Send an AJAX request
                                                        $.ajax({
                                                            url: "<?= base_url('delete_phases'); ?>", // Update this with your controller method
                                                            type: 'POST',
                                                            data: formData,
                                                            contentType: false,
                                                            processData: false,
                                                            beforeSend: function() {
                                                                // Display a loading indicator
                                                                $('#delete_phasesBtn<?= $ph['id'] ?>').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Deleting...');
                                                            },
                                                            success: function(response) {
                                                                // Handle the success response
                                                                console.log(response.success);

                                                                // Optionally, display a success message to the user
                                                                if (response.status === 'success') {
                                                                    // Display a success message to the user
                                                                    toastr.success(response.message);

                                                                    // Reload page after 1 second
                                                                    setTimeout(function() {
                                                                        location.reload();
                                                                    }, 1000);
                                                                } else {
                                                                    // Display an error message to the user
                                                                    toastr.error(response.message);

                                                                    // Reload page after 1 second
                                                                    setTimeout(function() {
                                                                        location.reload();
                                                                    }, 2000);
                                                                }


                                                            },
                                                            error: function(error) {
                                                                // Handle the error response
                                                                console.log("Errrrrooorr");

                                                                // Optionally, display an error message to the user
                                                                toastr.error(response.message);
                                                            }
                                                        });
                                                    });
                                                });
                                            </script>

                                        </div>
                                    </div>
                                </div>

                            </li>
                            <li class="list-group-item p-0">

                                <ul class="list-group">
                                    <?php foreach ($milestones as $ms) :
                                        if ($ms['phase_id'] == $ph['id']) {
                                    ?>
                                            <li class="list-group-item "> <i class="fa fa-check" aria-hidden="true"></i> <?= $ms['milestones'] ?> <span class=" float-right"><?= get_status_icon($ms['checked']) ?></span> </li>
                                        <?php
                                        }
                                        ?>

                                    <?php endforeach; ?>
                                </ul>
                            </li>
                        <?php endforeach; ?>
                    </ul>

                </div>

            </div>
        </div>
    </div>
</section>


</body>

<script>
    $(document).ready(function() {
        $('#province').show(function() {
            var province_code = $(this).val();

            $.ajax({
                url: '<?= base_url() ?>getaddress',
                type: 'post',
                data: {
                    province_code: province_code
                },
                dataType: 'json',
                success: function(response) {
                    var len = response.district.length;

                    $("#district").empty();
                    $("#district").append("<option value='<?= $get_district['districtcode'] ?>'><?= $get_district['name'] ?></option>");

                    for (var i = 0; i < len; i++) {
                        var code = response.district[i]['districtcode'];
                        var name = response.district[i]['name'];
                        //var code = response.subcategories[i]['code'];

                        $("#district").append("<option value='" + code + "'>" + name +
                            "</option>");

                    }
                }
            });
        });
    });
</script>



<?= $this->endSection() ?>