INFO - 2024-02-23 08:18:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:30:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:32:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:32:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:34:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:35:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:38:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:40:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:40:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:43:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:44:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:45:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:46:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:47:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:48:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 09:49:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2024-02-23 09:49:36 --> [DEPRECATED] ucwords(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucwords(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:49:36 --> [DEPRECATED] ucwords(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucwords(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:49:36 --> [DEPRECATED] ucwords(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucwords(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:49:36 --> [DEPRECATED] ucwords(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucwords(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:49:36 --> [DEPRECATED] ucwords(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucwords(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:49:36 --> [DEPRECATED] ucwords(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucwords(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2024-02-23 09:49:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2024-02-23 09:49:53 --> [DEPRECATED] ucfirst(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucfirst(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:49:53 --> [DEPRECATED] ucfirst(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucfirst(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:49:53 --> [DEPRECATED] ucfirst(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucfirst(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:49:53 --> [DEPRECATED] ucfirst(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucfirst(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:49:53 --> [DEPRECATED] ucfirst(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucfirst(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:49:53 --> [DEPRECATED] ucfirst(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): ucfirst(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2024-02-23 09:50:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2024-02-23 09:50:02 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:50:02 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:50:02 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:50:02 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:50:02 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 09:50:02 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2024-02-23 10:05:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2024-02-23 10:05:13 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 10:05:13 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 10:05:13 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 10:05:13 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 10:05:13 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2024-02-23 10:05:13 --> [DEPRECATED] strtoupper(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\admindash\reports_dashboard.php on line 243.
 1 APPPATH\Views\admindash\reports_dashboard.php(243): strtoupper(null)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\admindash\\reports_dashboard.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/reports_dashboard', [], true)
 5 APPPATH\Controllers\Admindash.php(303): view('admindash/reports_dashboard', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->reports_dashboard()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2024-02-23 10:06:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:11:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:13:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:42:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:43:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:43:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:45:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:45:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:46:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:46:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:46:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:47:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:47:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:48:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 10:50:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:01:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:02:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:03:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:07:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:08:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:10:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:12:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:13:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:16:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:17:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:19:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:20:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:21:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:23:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:25:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:27:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:28:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:28:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:29:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:29:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:30:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:31:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:32:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:33:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:33:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:33:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:33:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:33:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:33:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:42:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:43:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:44:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:48:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:48:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:48:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 11:48:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 12:16:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 12:32:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 12:38:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 12:39:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 12:40:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 12:40:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 12:40:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 12:45:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 12:52:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 12:54:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 13:02:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 13:03:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 13:04:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 13:05:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 13:05:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 13:11:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2024-02-23 14:00:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
