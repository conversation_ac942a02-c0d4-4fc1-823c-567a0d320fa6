<?= $this->extend("templates/nolsadmintemp"); ?>
<?= $this->section('content'); ?>

<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.3/html2pdf.bundle.min.js"></script>

<section class="container-fluid d-print-none ">
    <div class="row p-1">
        <div class="col-12 d-flex justify-content-between">

            <h4><?= $pro['procode'] . ": " . $pro['name'] ?></h4>

            <nav class="breadcrumb">
                <a class="breadcrumb-item" href="<?= base_url() ?>po_open_project/<?= $pro['ucode'] ?>"> <i class="bi bi-chevron-left"></i> Go Back</a>
                <!-- <a class="breadcrumb-item" href="#"></a> -->
                <span class="breadcrumb-item active"><?= $pro['procode'] ?></span>
                <button class="breadcrumb-item active btn btn-flat btn-sm" onclick="printPDF()"><i class="fas fa-download" aria-hidden="true"></i> PDF</button>
            </nav>

        </div>

    </div>

</section>

<section class=" container-fluid content" id="printPDF">
    <div class="row">
        <div class="col-md-12 text-center">
            <h2 class="">Project Report</h2>
        </div>
    </div>
    <div class="row mt-2 mb-2">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    Project Charts
                    <span class=" float-right"><b><?= $pro['name'] ?> /<?= $pro['procode'] ?> </b></span>
                </div>
                <div class="card-body p-0">
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-md-6 text-center">
                                    <?php
                                    $outstanding = $pro['budget'] - $pro['payment_total'];
                                    // Data values 
                                    if ($outstanding <= 0) {
                                        $outstanding = 0;
                                    }
                                    $data_payments = [($pro['payment_total']), (($outstanding))];
                                    // Data labels
                                    $labels_payments = ["Paid", "Outstanding"];
                                    ?>
                                    <figure class="figure border-2 border-secondary " style="border: 2px solid black;">

                                        <canvas id="pieChart_payments" style="width:300px"></canvas>
                                        <figcaption class="figure-caption text-dark bg-secondary">Payments
                                        </figcaption>
                                        <script>
                                            // Setup block
                                            const data_payments = <?php echo json_encode($data_payments); ?>;
                                            const labels_payments = <?php echo json_encode($labels_payments); ?>;
                                            const config_pieChart_payments = {
                                                type: 'pie',
                                                data: {
                                                    datasets: [{
                                                        data: data_payments,
                                                        backgroundColor: [
                                                            "#28a745",
                                                            "#ffc107",
                                                        ]
                                                    }],
                                                    labels: labels_payments
                                                },
                                                options: {
                                                    responsive: true
                                                }
                                            };

                                            // Render block
                                            const pieChart_payments = new Chart(
                                                document.getElementById('pieChart_payments'),
                                                config_pieChart_payments
                                            );
                                        </script>

                                    </figure>
                                </div>

                                <div class="col-md-6 pl-1 text-center">
                                    <figure class="figure border-2 border-secondary " style="border: 2px solid black;">

                                        <canvas id="doughnutChart_milestone" style="max-width:100%" class=" figure-img img-fluid"></canvas>
                                        <figcaption class="figure-caption text-center text-dark bg-secondary">Milestones by %</figcaption>
                                        <?php
                                        // Data values 
                                        $data_milestone = [($pro_ms_pending), ($pro_ms_completed), ($pro_ms_hold), ($pro_ms_canceled)];

                                        // Data labels
                                        $labels_milestone = ['Pending', 'Completed', 'Hold', 'Canceled'];
                                        ?>
                                        <script>
                                            const data_milestone = <?php echo json_encode($data_milestone); ?>;
                                            const labels_milestone = <?php echo json_encode($labels_milestone); ?>;

                                            const config_doughnutChart_milestone = {
                                                type: 'doughnut',
                                                data: {
                                                    datasets: [{
                                                        data: data_milestone,
                                                        backgroundColor: [
                                                            "#007bff",
                                                            "#28a745",
                                                            "#ffc107",
                                                            "#FF3D00",

                                                        ],
                                                        hoverBackgroundColor: [
                                                            "#007bff",
                                                            "#28a745",
                                                            "#ffc107",
                                                            "#FF3D00"

                                                        ]
                                                    }],
                                                    labels: labels_milestone
                                                },
                                                options: {
                                                    responsive: true,
                                                    legend: {
                                                        position: 'top',
                                                    },
                                                    title: {
                                                        display: false,
                                                        text: 'Doughnut Chart'
                                                    },
                                                    animation: {
                                                        animateScale: true,
                                                        animateRotate: true
                                                    }
                                                }
                                            };

                                            const doughnutChart_doughnutChart_milestone = new Chart(
                                                document.getElementById('doughnutChart_milestone'),
                                                config_doughnutChart_milestone
                                            );
                                        </script>

                                    </figure>
                                </div>
                                
                            </div>
                        </div>

                        <div class="col-md-6 text-center">
                            <figure class="figure border-2 border-secondary text-center" style="border: 2px solid black;">

                                <canvas id="barChart_milephase" style="width:100%; height:300px" class=" figure-img img-fluid"></canvas>
                                <figcaption class="figure-caption text-center text-dark bg-secondary">Phases & Milestones %</figcaption>


                                <?php
                                // Data labels
                                $labels_milephase = "";
                                $data_milephase_pending = $data_milephase_completed = $data_milephase_hold = "";
                                $data_milestone_pending = $data_milestone_completed = $data_milestone_hold = array();


                                //echo "--- ECHOO::". count($phases);
                                foreach ($phases as $ph) {
                                    $milephase_pending = $milephase_completed = $milephase_hold = $milephase_canceled = 0;
                                    $labels_milephase .= "'" . $ph['phases'] . "',";
                                    foreach ($milestones as $ms) {
                                        if ($ms['phase_id'] == $ph['id']) {
                                            if ($ms['checked'] == 'completed') {
                                                $milephase_completed++;
                                            }
                                            if ($ms['checked'] == 'pending') {
                                                $milephase_pending++;
                                            }
                                            if ($ms['checked'] == 'hold') {
                                                $milephase_hold++;
                                            }
                                            if ($ms['checked'] == 'canceled') {
                                                $milephase_canceled++;
                                            }
                                        }
                                    }
                                    $data_milestone_pending[] = $milephase_pending;
                                    $data_milestone_completed[] = $milephase_completed;
                                    $data_milestone_hold[] = $milephase_hold;
                                    $data_milephase_pending .= ($milephase_pending) . ',';
                                    $data_milephase_completed .= ($milephase_completed) . ',';
                                    $data_milephase_hold .= ($milephase_hold) . ',';
                                }
                                //echo $labels_milephase;
                                // echo $data_milephase_pending;
                                ?>

                                <script>
                                    var stackedBar = document.getElementById('barChart_milephase').getContext('2d');

                                    var myStackedBar = new Chart(stackedBar, {
                                        type: 'bar',
                                        data: {
                                            labels: [<?= $labels_milephase ?>],
                                            datasets: [

                                                {
                                                    label: 'Pending',
                                                    data: [<?= $data_milephase_pending ?>],
                                                    backgroundColor: '#007bff',
                                                }, {
                                                    label: 'Completed',
                                                    data: [<?= $data_milephase_completed ?>],
                                                    backgroundColor: '#28a745'
                                                },
                                                {
                                                    label: 'Hold',
                                                    data: [<?= $data_milephase_hold ?>],
                                                    backgroundColor: '#ffc107'
                                                },

                                            ]
                                        },
                                        options: {
                                            title: {
                                                display: false,
                                                text: 'Stacked Bar Chart'
                                            },
                                            tooltips: {
                                                mode: 'index',
                                                intersect: false
                                            },
                                            responsive: true,
                                            scales: {
                                                xAxes: [{
                                                    stacked: true,
                                                }],
                                                yAxes: [{
                                                    stacked: true
                                                }]
                                            }
                                        }
                                    });
                                </script>

                            </figure>
                        </div>
                    </div>


                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <b>Budget: <span class=" float-right"><?= COUNTRY_CURRENCY . " " . number_format($pro['budget'], 2) ?></span> </b>
                            <div class=" d-flex justify-content-between">
                                <span>Outstanding: <?= COUNTRY_CURRENCY . " " . number_format(($pro['budget'] - $pro['payment_total']), 2) ?> </span>
                                <span>Paid: <?= COUNTRY_CURRENCY . " " . number_format($pro['payment_total'], 2) ?> </span>
                            </div>

                        </div>
                        <div class="col-md-6">
                            <b>Milestones by %:</b>
                            <div class=" d-flex justify-content-between">
                                <span>Pending: <?= $mspend = round($ms_pending_percent, 2) ?>%</span>
                                <span>Completed:
                                    <?= $mscomp = round($ms_completed_percent, 2) ?>%
                                </span>


                                <span>Hold: <?= $mshold = round($ms_hold_percent, 2) ?>% </span>
                                <span>Canceled: <?= $mshold = round($ms_canceled_percent, 2) ?>% </span>
                            </div>

                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <hr>
    <div class="row mt-2 mb-2">
        <div class="col-md-6 mb-1">
            <div class="card">
                <div class="card-header">
                    <i class="fa fa-info-circle" aria-hidden="true"></i> Information
                </div>
                <div class="card-body p-0">
                    <ul class="list-group border-0">
                        <!--tips: add .list-group-flush to the .list-group to remove some borders and rounded corners-->

                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Pro.Code:</b>
                            <span class=""><?= $pro['procode'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Pro.Name:</b>
                            <span class=""><?= $pro['name'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Pro.Description:</b>
                            <span class=""><?= $pro['description'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Project Site:</b>
                            <span class=""><?= $pro['pro_site'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Loc.Address:</b>
                            <span class=""><?= $country['name'] ?>, <?= $province['name'] ?>, <?= $dist['name'] ?>
                                <?php if (!empty($llg['name'])) : ?>
                                    ,<?= $llg['name'] ?>
                                <?php endif; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Loc.GPS:</b>
                            <span class=""><b>Lat:</b> <?= $pro['lat'] ?> <b>Lon:</b> <?= $pro['lon'] ?></span>
                        </li>

                    </ul>
                </div>
                <div class="card-footer">
                    <em>Created:

                        <span class=" float-right"> <?= datetimeforms($pro['create_at']) ?> / <?= $pro['create_by'] ?></span>
                    </em>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fa fa-info-circle" aria-hidden="true"></i> Details
                </div>
                <div class="card-body p-0">
                    <ul class="list-group border-0">
                        <!--tips: add .list-group-flush to the .list-group to remove some borders and rounded corners-->

                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Pro.Budget:</b>
                            <span class=""><?= COUNTRY_CURRENCY ?> <?= number_format($pro['budget'], 2) ?></span>
                        </li>

                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Fund.Source:</b>
                            <span class=""><?= strtoupper($pro['fund']) ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Pro.Officer:</b>
                            <span class=""><?= $pro['pro_officer_name'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Pro.Contractor:</b>
                            <span class=""><?= $pro['contractor_name'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Pro.Status:</b>
                            <span class=""><?= $pro['status'] ?></span>
                        </li>

                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <b>Pro.Status Notes:</b>
                            <span class=""><?= $pro['statusnotes'] ?></span>
                        </li>

                    </ul>
                </div>
                <div class="card-footer">
                    <em>Last Update:

                        <span class=" float-right"> <?= datetimeforms($pro['update_at']) ?> / <?= $pro['update_by'] ?></span>
                    </em>
                </div>
            </div>
            <!-- /. card -->
        </div>
        <!-- ./col -->
    </div>
    <!-- ./row -->

    <div class="row mt-2 mb-2">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    Map: <em><?= $pro['procode'] ?></em>
                </div>
                <div class="card-body p-0">
                    <!-- 
                  s  extracting kml file into map
                 -->
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/openlayers/4.6.5/ol.js"></script>
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/openlayers/4.6.5/ol.css" type="text/css">

                    <div id="map" style="max-width:100%; height: 500px;"></div>

                    <!-- Add this line -->
                    <script>
                        var vectorSources = [
                            new ol.source.Vector({
                                url: "<?= base_url() . $pro['kmlfile'] ?>",
                                format: new ol.format.KML({
                                    extractStyles: false,
                                    extractAttributes: true,
                                }),
                                strategy: ol.loadingstrategy.bbox, // Only load visible features
                            }),
                            // add more VectorSource objects as needed
                        ];
                        var vectorLayers = [];
                        for (var i = 0; i < vectorSources.length; i++) {
                            var layer = new ol.layer.Vector({
                                source: vectorSources[i],
                                style: function(feature) {
                                    // Only display tracks
                                    if (feature.getGeometry().getType() === "LineString") {
                                        return new ol.style.Style({
                                            stroke: new ol.style.Stroke({
                                                color: "red",
                                                width: 2,
                                            }),
                                        });
                                    }
                                },
                            });
                            vectorLayers.push(layer);
                        }
                        var marker1 = new ol.Feature({
                            geometry: new ol.geom.Point(
                                ol.proj.fromLonLat([<?= $pro['lon'] ?>, <?= $pro['lat'] ?>])
                            ),
                            name: "Marker 1",
                            description: "This is the first marker",
                            url: "https://govhrm.wanspeen.com",
                            link: "View",
                        });

                        var vectorPoints = new ol.source.Vector({
                            features: [marker1],
                        });
                        var vectorPointsLayer = new ol.layer.Vector({
                            source: vectorPoints,
                            style: new ol.style.Style({
                                image: new ol.style.Icon({
                                    src: "<?= base_url() ?>public/assets/system_img/marker-map.png",
                                    // size:[20,20]
                                }),
                            }),
                        });
                        var map = new ol.Map({
                            layers: [
                                new ol.layer.Tile({
                                    source: new ol.source.OSM(),
                                }),
                                // add all VectorLayer objects to the layers array
                                ...vectorLayers,
                                vectorPointsLayer,
                            ],
                            target: "map",
                            view: new ol.View({
                                center: ol.proj.fromLonLat([<?= $pro['lon'] ?>, <?= $pro['lat'] ?>]),
                                zoom: 14,
                            }),
                        });
                    </script>

                    <!-- 
                        End extract kml files to Map
                     -->
                </div>

            </div>
        </div>
    </div>
    <hr>
    <div class="row mt-2 mb-2">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    Phases and Milestones: <em><?= $pro['procode'] ?></em>
                </div>
                <div class="card-body p-0 table-responsive-md">
                    <table class="table table-bordered text-nowrap ">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Milestones</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Remarks</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $pplus = 1;
                            foreach ($phases as $ph) : ?>
                                <tr>
                                    <td colspan="5"><?= $pplus; ?>. <?= $ph['phases']; ?></td>
                                </tr>
                                <?php $msplus = 1;
                                foreach ($milestones as $ms) :
                                    if ($ms['phase_id'] == $ph['id']) :
                                ?>
                                        <tr>
                                            <td><?= $pplus ?>.<?= $msplus++ ?> </td>
                                            <td><?= $ms['milestones'] ?></td>
                                            <td><?= $ms['checked'] ?></td>
                                            <td><?= dateforms($ms['update_at']) ?></td>
                                            <td><?= $ms['notes'] ?></td>
                                        </tr>
                                <?php
                                    endif;
                                endforeach; ?>
                            <?php $pplus++;
                            endforeach; ?>


                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
    <hr>
    <div class="row mt-2 mb-2">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    Payments: <em><?= $pro['procode'] ?></em>
                    <span class=" float-right"><b><?= COUNTRY_CURRENCY ?> <?= number_format($pro['budget'], 2) ?> </b></span>
                </div>
                <div class="card-body p-0 table-responsive-md">
                    <table class="table table-bordered text-nowrap ">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Date</th>
                                <th>Amount(PGK)</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $pplus = 1;
                            $pay = array();
                            foreach ($payments as $py) : ?>
                                <tr>
                                    <td><?= $pplus++ ?></td>
                                    <td><?= dateforms($py['paymentdate']) ?></td>
                                    <td><?php
                                        echo  number_format($py['amount'], 2);
                                        $pay[] = ($py['amount']);
                                        ?></td>
                                    <td><?= $py['description'] ?></td>
                                </tr>
                            <?php
                            endforeach; ?>
                        <tfoot class=" font-weight-bold">
                            <tr>
                                <td colspan="2">Total Payment</td>
                                <td><?= number_format(array_sum($pay), 2) ?></td>
                                <td>Total Outstanding: (<?= number_format($pro['budget'], 2) ?> - <?= number_format(array_sum($pay), 2) ?>) = <span class=" font-weight-bold float-right"> <?= COUNTRY_CURRENCY ?>
                                        <?= $outstanding = $pro['budget'] - array_sum($pay) ?>
                                        <?php if ($outstanding < 0) {
                                            echo '(Overpayment)';
                                        } ?>

                                    </span></td>
                            </tr>
                        </tfoot>

                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
    <hr>
    <div class="row mt-2 mb-2">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    Events: <em><?= $pro['procode'] ?></em>
                </div>
                <div class="card-body p-0 table-responsive-md">
                    <table class="table table-bordered text-nowrap">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Date</th>
                                <th>Event</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $pplus = 1;
                            foreach ($events as $ev) : ?>
                                <tr>
                                    <td><?= $pplus++ ?></td>
                                    <td><?= dateforms($ev['eventdate']) ?></td>
                                    <td><?= $ev['event'] ?></td>
                                </tr>
                            <?php
                            endforeach; ?>

                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>




    <script>
        function printPDF() {

            // Options
            var opt = {
                margin: 0.5,
                filename: 'report.pdf',
                image: {
                    type: 'jpeg',
                    quality: 1.98
                },
                html2canvas: {
                    dpi: 200,
                    letterRendering: true,
                    useCORS: true
                },
                jsPDF: {
                    unit: 'in',
                    format: 'A3',
                    orientation: 'landscape'
                }
            };

            // New Promise-based usage:
            // html2pdf().set(opt).from('document.body').save();

            // Get the <ul> element
            const list = document.querySelector('#printPDF');

            // Generate PDF from <ul> only  
            html2pdf().from(list).save();

        }
    </script>


</section>



<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.slim.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>

<?= $this->endSection() ?>