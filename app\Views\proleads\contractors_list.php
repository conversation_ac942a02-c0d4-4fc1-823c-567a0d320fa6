<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>

<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.bootstrap4.min.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.bootstrap4.min.js" ></script>


<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">

            <div class="col-sm-6">
                <h1 class="m-0">Contractors List</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item "><a href="<?= base_url() ?>dashboard" class="btn btn-sm btn-primary"> <i class="fa fa-arrow-left" aria-hidden="true"></i> Go Back </a></li>
                    <li class="breadcrumb-item active">Contractors List</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->


<section class=" container-fluid">

    <div class="row pt-2">

        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary">
                    <i class="fas fa-toolbox    "></i> CONTRACTORS LIST
                    <a href="<?= base_url() ?>contractors_new" class="btn btn-dark btn-sm float-right"> <i class="fa fa-plus" aria-hidden="true"></i> Add New Contractor</a>
                </div>
                <div class="card-body p-0">
                    <table class="table table-bordered table-hover" id="contractors">
                        <thead class="">
                            <tr>
                                <th>Code</th>
                                <th>Name</th>
                                <th>Category</th>
                                <th>Flag</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($contractors as $con) : ?>
                                <tr>
                                    <td><?= $con['concode'] ?></td>
                                    <td scope="row"><?= $con['name'] ?></td>
                                    <td>
                                        <?php foreach ($con_category as $cat) :
                                            if ($cat['value'] == $con['category']) :
                                        ?>
                                                <?= $cat['item'] ?>
                                        <?php endif;
                                        endforeach; ?>
                                    </td>
                                    <td>
                                        <?php $xx = 0;
                                        foreach ($notices as $note) :
                                            if ($con['concode'] == $note['concode'] && $xx++ < 1) :
                                        ?>
                                                <?= get_notice_flags($note['notice_flag']) ?>
                                            <?php endif; ?>

                                        <?php endforeach; ?>
                                    </td>
                                    <td>
                                        <a href="<?= base_url() ?>open_contractor/<?= $con['ucode'] ?>" class="btn btn-primary btn-sm float-right"> View <i class="fa fa-angle-right" aria-hidden="true"></i> </a>

                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="card-footer">
                    <em> <i class="fa fa-info-circle" aria-hidden="true"></i> Table showing list of Contractors</em>
                </div>
            </div>
        </div>

    </div>
</section>

<script>
    $(document).ready(function() {
        $('#contractors').DataTable();
    });
</script>


</body>


<?= $this->endSection() ?>