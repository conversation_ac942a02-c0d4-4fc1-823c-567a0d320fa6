<?= $this->extend("templates/nolstemp"); ?>
<?= $this->section('content'); ?>

<section class="container-fluid">

    <!-- Include DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <ul class="nav nav-tabs card-header-tabs" id="projectTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link text-white active" id="map-tab" data-bs-toggle="tab" href="#map-content" role="tab" aria-controls="map-content" aria-selected="true">
                                <i class="fas fa-map-marked-alt me-2"></i>Projects Map
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" id="list-tab" data-bs-toggle="tab" href="#list-content" role="tab" aria-controls="list-content" aria-selected="false">
                                <i class="fas fa-list me-2"></i>Projects List
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body p-0">
                    <div class="tab-content" id="projectTabsContent">
                        <div class="tab-pane fade show active" id="map-content" role="tabpanel" aria-labelledby="map-tab">
                            <div class="p-3">
                                <!-- Include Leaflet CSS -->
                                <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" crossorigin="" />
                                <!-- Include Leaflet.awesome-markers CSS -->
                                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css" />
                                <!-- Include Leaflet.KML plugin -->
                                <script src="https://unpkg.com/leaflet-omnivore/leaflet-omnivore.min.js"></script>
                                
                                <style>
                                    #map {
                                        height: 700px;
                                        border-radius: 0.5rem;
                                        box-shadow: var(--card-shadow);
                                    }
                                </style>

                                <!-- Map container with shadow and rounded corners -->
                                <div id="map" class="mb-3"></div>

                                <!-- Include Leaflet JavaScript -->
                                <script src="https://unpkg.com/leaflet/dist/leaflet.js" crossorigin=""></script>
                                <!-- Include Leaflet.awesome-markers JavaScript -->
                                <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.min.js"></script>


                                <!-- Initialize Leaflet map -->
                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Initialize Leaflet map
                                        const map = L.map('map').setView([-5.583301, 147.152774], 6.5); // Center map and set zoom level

                                        // Add OpenStreetMap tile layer with improved styling
                                        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                                            maxZoom: 19
                                        }).addTo(map);

                                        // Function to add KML layers with improved styling
                                        function addKMLLayer(kmlURL) {
                                            omnivore.kml(kmlURL, null, L.geoJSON(null, {
                                                style: {
                                                    color: 'var(--primary-color)',
                                                    weight: 3,
                                                    opacity: 0.7,
                                                    fillOpacity: 0.4
                                                }
                                            })).addTo(map);
                                        }

                                        // Function to add coordinates markers with custom icons
                                        function addMarkers(coordinates) {
                                            coordinates.forEach(coord => {
                                                const marker = L.marker(coord, {
                                                    icon: L.AwesomeMarkers.icon({
                                                        icon: 'map-pin',
                                                        markerColor: 'blue',
                                                        prefix: 'fa'
                                                    })
                                                }).addTo(map);
                                            });
                                        }

                                        // Add KML files
                                        <?php foreach ($pro as $p) : ?>
                                            addKMLLayer('<?= base_url() . $p['kmlfile']; ?>');
                                        <?php endforeach ?>
                                        
                                        // Add coordinates
                                        const coordinates = [
                                            <?php foreach ($pro as $p) : ?>[<?= $p['gps'] ?>],
                                            <?php endforeach ?>
                                        ];
                                        addMarkers(coordinates);
                                    });
                                </script>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="list-content" role="tabpanel" aria-labelledby="list-tab">
                            <div class="p-3">
                                <div class="table-responsive">
                                    <table class="table table-hover table-striped" id="projects_list">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>#</th>
                                                <th>Code</th>
                                                <th>Project</th>
                                                <th>T.Payments</th>
                                                <th>Milestones</th>
                                                <th>Contractor</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $count = 1;
                                            foreach ($pro as $p) : ?>
                                                <tr class="cursor-pointer" onclick="window.location='<?= base_url() ?>home_project_one_view/<?= $p['ucode'] ?>'" style="cursor: pointer;">
                                                    <td><?= $count++ ?></td>
                                                    <td><span class="badge bg-primary"><?= $p['procode'] ?></span></td>
                                                    <td><?= $p['name'] ?></td>

                                                    <?php $amount = array();
                                                    foreach ($payments as $pay) {
                                                        if ($pay['procode'] == $p['procode']) {
                                                            $amount[] = $pay['amount'];
                                                        }
                                                    } ?>

                                                    <td>
                                                        <?= number_format(array_sum($amount), 2) ?>
                                                        <?php
                                                        if (array_sum($amount) != 0) {
                                                            $percentage = round(number_format((array_sum($amount) / ($p['budget'])) * 100, 0), 2);
                                                            echo "<div class='progress mt-1' style='height: 6px;'>
                                                                <div class='progress-bar' role='progressbar' style='width: {$percentage}%;' aria-valuenow='{$percentage}' aria-valuemin='0' aria-valuemax='100'></div>
                                                            </div>";
                                                            echo "<small class='text-muted'>({$percentage}%)</small>";
                                                        }
                                                        ?>
                                                    </td>

                                                    <?php $allmiles = $compmiles = array();
                                                    foreach ($milestones as $ms) {
                                                        if ($ms['procode'] == $p['procode']) {
                                                            $allmiles[] = $ms['milestones'];

                                                            if ($ms['checked'] == 'completed') {
                                                                $compmiles[] = $ms['milestones'];
                                                            }
                                                        }
                                                    } ?>

                                                    <td>
                                                        <?php
                                                        if (count($allmiles) != 0) {
                                                            $percentage = round((count($compmiles) / count($allmiles)) * 100, 2);
                                                            echo "<div class='progress' style='height: 6px;'>
                                                                <div class='progress-bar bg-success' role='progressbar' style='width: {$percentage}%;' aria-valuenow='{$percentage}' aria-valuemin='0' aria-valuemax='100'></div>
                                                            </div>";
                                                            echo "<small class='text-muted'>{$percentage}%</small>";
                                                        } else {
                                                            echo "<span class='text-muted'>No milestones</span>";
                                                        }
                                                        ?>
                                                    </td>
                                                    <td><?= $p['contractor_name'] ?></td>
                                                    <td>
                                                        <?php 
                                                        $statusClass = 'bg-secondary';
                                                        if ($p['status'] == 'Completed') $statusClass = 'bg-success';
                                                        if ($p['status'] == 'In Progress') $statusClass = 'bg-primary';
                                                        if ($p['status'] == 'Delayed') $statusClass = 'bg-warning';
                                                        if ($p['status'] == 'Cancelled') $statusClass = 'bg-danger';
                                                        ?>
                                                        <span class="badge <?= $statusClass ?>"><?= $p['status'] ?></span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- DataTables JavaScript -->
                                <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                                <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
                                <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Initialize DataTables with improved options
                                        $('#projects_list').DataTable({
                                            responsive: true,
                                            language: {
                                                search: "<i class='fas fa-search'></i> Search:",
                                                lengthMenu: "Show _MENU_ entries",
                                                info: "Showing _START_ to _END_ of _TOTAL_ projects"
                                            }
                                        });
                                    });
                                </script>

                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?= $this->endSection() ?>