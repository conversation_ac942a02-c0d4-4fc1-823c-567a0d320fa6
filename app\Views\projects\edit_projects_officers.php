<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>


<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $pro['name'] ?></h1>
                <h5><?= $pro['procode'] ?></h5>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>open_projects/<?= $pro['ucode'] ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true"></i> View Project</a></li>
                    <li class="breadcrumb-item active"><?= $pro['name'] ?></li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">
    <div class="row p-2">
        <div class=" col-md-12">
            <div class="card">
                <div class="card-header bg-info">

                    <a class="btn btn-dark btn-sm " href="<?= base_url() ?>open_projects/<?= $pro['ucode'] ?>"><i class="fa fa-arrow-circle-left " aria-hidden="true"></i> <span class=" ">Back</span> </a>

                    <div class="float-right">Edit Project Officer</div>

                    

                </div>
                <?= form_open_multipart("update_projects_officers") ?>
                <div class="card-body">

                    <div class="row">

                        <div class="form-group col-md-12">
                            <label for="">Project Officer</label>
                            <select class="form-control" name="project_officers" id="">
                                <option selected value="<?= $pro['pro_officer_id'] ?>"><b><?= $pro['pro_officer_name'] ?></b></option>
                                <?php foreach ($pro_officers as $po) : ?>
                                    <option value="<?= $po['id'] ?>"><?= $po['name'] ?> | <?= $po['username'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="my-input">Project Officer Work Scope</label>
                            <textarea name="work_scope" class="form-control" id="" cols="30" rows="5" placeholder="Enter Work Scope"><?= $pro['pro_officer_scope'] ?></textarea>
                        </div>

                    </div>

                </div>
                <!-- .body -->
                <div class="card-footer">
                    <input type="hidden" name="pro_ucode" value="<?= $pro['ucode'] ?>">
                    <input type="hidden" name="pro_id" value="<?= $pro['id'] ?>">
                    <button type="submit" class="btn btn-info float-right"> <i class="fas fa-save    "></i> Save Changes</button>
                </div>

                </form>


            </div>
            <!-- .card -->


            <div class="callout callout-info">
                <h5 class=" text-left"><?= $pro['pro_officer_name'] ?></h5>

                <span class=" text-right">
                    <b>Scope: </b>
                    <p></p><?= $pro['pro_officer_scope'] ?></p>
                </span>
            </div>


        </div>
        <!-- .col -->
    </div>
    <!-- .row -->
</section>


</body>




<?= $this->endSection() ?>