<?= $this->extend("templates/nolsadmintemp"); ?>
<?= $this->section('content'); ?>

<body>
    <div class="container-fluid">
        <div class="row p-1">
            <div class="col-12 d-flex justify-content-between">

                <h4><?= $pro['procode'] . "-" . $pro['name'] ?></h4>

                <nav class="breadcrumb">
                    <a class="breadcrumb-item" href="<?= base_url() ?>/po_phases/<?= $pro['ucode'] ?>"> <i class="bi bi-chevron-left"></i> Go Back</a>
                    <span class="breadcrumb-item active">Phases</span>
                    <span class="breadcrumb-item active">Milestones</span>
                </nav>

            </div>

        </div>

        <div class="row pb-2">
            <div class="col-md-12">
                <h5 class="text-center">MILESTONES</h5>
            </div>
        </div>

        <div class="row">

            <div class="col-md-12">



                <!-- Button trigger modal -->
                <button type="button" class="btn btn-primary justify-content-end float-right m-1" data-toggle="modal" data-target="#check" hover>
                    <i class=" fas fa-check" aria-hidden="true"></i> Set Milestone
                </button>
                <!-- Button trigger modal -->
                <button type="button" class="btn btn-primary justify-content-end float-right m-1" data-toggle="modal" data-target="#addmilefile" hover>
                    <i class=" fa fa-upload" aria-hidden="true"></i> Upload Milestone Files
                </button>

                <!-- Modal notes -->
                <div class="modal fade" id="check" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"> <i class="fas fa-check    "></i> Set Milestone</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <?= form_open_multipart('milestone_notes') ?>
                            <div class="modal-body">
                                <div class="form-group">
                                    <label for="my-textarea">Notes</label>
                                    <textarea id="my-textarea" class="form-control" name="milenotes" rows="3" placeholder="Write something here"><?= $milestones['notes'] ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="my-textarea">Status</label>
                                    <select name="check" id="" class="form-control border-1" required>
                                        <?php if (!empty($milestones['checked'])) : ?>
                                            <option selected="selected" value="<?= $milestones['checked'] ?>"><?= ucfirst($milestones['checked']) ?></option>
                                        <?php else : ?>
                                            <option value="">--Select Milestone Status--</option>
                                        <?php endif; ?>
                                        <option value="completed">Completed</option>
                                        <option value="pending">Pending</option>
                                        <option value="hold">Hold</option>
                                        <option value="canceled">Canceled</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="my-textarea">Status Date</label>
                                    <input type="date" name="milesdate" id="" class="form-control" value="<?= $milestones['checked_date'] ?>" required>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <input type="hidden" name="ms_id" value="<?= $milestones['id'] ?>">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                <button type="submit" class="btn btn-primary">Save</button>
                            </div>
                            <?= form_close(); ?>
                        </div>
                    </div>
                </div>
                <!-- ./modal -->

                <!-- Modal upload files -->
                <div class="modal fade" id="addmilefile" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"> <i class="fa fa-upload" aria-hidden="true"></i> Upload Milestone Files</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <?= form_open_multipart('milestone_files') ?>
                            <div class="modal-body">

                                <div class="mb-3">
                                    <label for="formFileMultiple" class="form-label">Multiple files Upload</label>
                                    <input class="form-control" name="milefiles[]" type="file" id="formFileMultiple" multiple>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <input type="hidden" name="ms_id" value="<?= $milestones['id'] ?>">
                                <input type="hidden" name="ph_id" value="<?= $phases['id'] ?>">
                                <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                <button type="submit" class="btn btn-primary"> <i class="fa fa-upload" aria-hidden="true"></i> Upload</button>
                            </div>
                            <?= form_close(); ?>
                        </div>
                    </div>
                </div>
                <!-- ./modal -->




            </div>

        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <?php
                    $bg = "bg-primary text-light";
                    $icon = "";
                    if ($milestones['checked'] == "completed") {
                        $bg = "bg-success text-light ";
                        $icon = "<i class='fa fa-check-circle'></i>";
                    }
                    if ($milestones['checked'] == "halted") {
                        $bg = "bg-warning text-dark ";
                        $icon = "<i class='fa fa-exclamation-triangle'></i>";
                    }
                    ?>
                    <div class="card-header <?= $bg ?>">
                        <h4 class=""> <?= $icon ?> <?= $milestones['milestones'] ?> <span class=" float-right"><?= dateforms($milestones['checked_date']) ?></span> </h4>
                    </div>
                    <div class="card-body">
                        <p><?= $milestones['notes'] ?></p>
                    </div>
                </div>
            </div>
        </div>


        <div class="row mb-2 ">
            <div class="col-md-12">
                <h2>Files</h2>
            </div>
            <?php foreach ($milefiles as $file) : ?>
                <div class="col-md-1 p-2">
                    <div class="card  p-0">
                        <img class="card-img-top" height="10%" src="<?= imgfilecheck($file['filepath']) ?>" alt="">

                        <div class="card-footer d-flex justify-content-between">
                            <a href="<?= base_url($file['filepath']) ?>" class=""> <i class="fa fa-download" aria-hidden="true"></i></a>

                            <!-- Button trigger modal -->
                            <a href="#" class=" text-danger" data-toggle="modal" data-target="#del<?= $file['id'] ?> "> <i class="fas fa-trash-alt" aria-hidden="true"></i></a>

                            <!-- Modal -->
                            <div class="modal fade" id="del<?= $file['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger">
                                            <h5 class="modal-title text-light"> <i class="fa fa-exclamation-triangle" aria-hidden="true"></i> Your aboout this delete</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <img class="card-img-top" src="<?= imgfilecheck($file['filepath']) ?>" alt="">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer border-3 border-danger">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                            <button type="submit" class="btn btn-danger"> <i class="fa fa-times-circle" aria-hidden="true"></i> Delete</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- ./modal -->

                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>












    </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.slim.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
</body>



</html>
<?= $this->endSection() ?>