INFO - 2023-09-18 02:22:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:22:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:41:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:41:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:41:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:41:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:42:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:42:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:42:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2023-09-18 02:42:36 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 02:42:36 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 02:42:36 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 02:42:36 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 02:42:36 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 02:42:36 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 02:42:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:42:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2023-09-18 02:42:41 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 02:42:41 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 02:42:41 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 02:42:41 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 02:42:41 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 02:42:41 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 02:51:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:51:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:51:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:51:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:51:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:51:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:51:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:51:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:51:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:51:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:51:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:51:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:54:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:55:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:55:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:55:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:56:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:56:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:57:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:57:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:58:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 02:58:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:00:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:03:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:05:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:05:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:06:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:06:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:07:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:08:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:13:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:13:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2023-09-18 03:13:48 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 03:13:48 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 03:13:48 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 03:13:48 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 03:13:48 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 03:13:48 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 03:13:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:17:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 03:17:40 --> count(): Argument #1 ($value) must be of type Countable|array, null given
in APPPATH\Views\admindash\dashboard.php on line 12.
 1 SYSTEMPATH\View\View.php(213): include()
 2 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 3 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('admindash/dashboard', [], true)
 4 APPPATH\Controllers\Admindash.php(67): view('admindash/dashboard', [...])
 5 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Admindash->index()
 6 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admindash))
 7 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 03:17:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:18:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:23:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:24:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:24:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:24:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:31:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:32:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:34:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:35:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:35:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:38:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:38:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:38:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:39:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:39:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 03:40:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:00:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:01:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:02:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:02:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:03:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:03:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:08:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:08:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:08:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:08:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:08:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:09:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:09:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:09:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2023-09-18 04:09:42 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 04:09:42 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 04:09:42 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 48.
 1 APPPATH\Views\projects\projects_list.php(48): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 04:09:42 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 04:09:42 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
WARNING - 2023-09-18 04:09:42 --> [DEPRECATED] number_format(): Passing null to parameter #1 ($num) of type float is deprecated in APPPATH\Views\projects\projects_list.php on line 99.
 1 APPPATH\Views\projects\projects_list.php(99): number_format(null, 2)
 2 SYSTEMPATH\View\View.php(213): include('APPPATH\\Views\\projects\\projects_list.php')
 3 SYSTEMPATH\View\View.php(216): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1184): CodeIgniter\View\View->render('projects/projects_list', [], true)
 5 APPPATH\Controllers\Projects.php(79): view('projects/projects_list', [...])
 6 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->index()
 7 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 8 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:09:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:11:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:11:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:16:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:16:01 --> There is no data to update.
in SYSTEMPATH\BaseModel.php on line 924.
 1 SYSTEMPATH\BaseModel.php(924): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(756): CodeIgniter\BaseModel->update([...], [])
 3 APPPATH\Controllers\Projects.php(417): CodeIgniter\Model->update('15', [...])
 4 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->gps_set()
 5 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 6 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:16:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:16:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:16:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:16:39 --> There is no data to update.
in SYSTEMPATH\BaseModel.php on line 924.
 1 SYSTEMPATH\BaseModel.php(924): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(756): CodeIgniter\BaseModel->update([...], [])
 3 APPPATH\Controllers\Projects.php(417): CodeIgniter\Model->update('15', [...])
 4 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->gps_set()
 5 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 6 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:17:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:17:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:17:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:17:26 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('FCPATH\\public', 'uploads', 'gps_files', '142023-01_1695010645.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:17:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:17:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:17:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:17:39 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('FCPATH\\public', 'uploads', 'gps_files', '142023-01_1695010659.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:19:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:20:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:20:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:20:04 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('FCPATH\\public', 'uploads', 'gps_files', '142023-01_1695010659.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:32:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:32:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:32:29 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlsda.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:32:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:32:30 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('marker-map.png')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:32:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:32:30 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmls', 'kmlwwk.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:32:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:32:30 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlwwkto.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:32:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:32:30 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('FCPATH\\public', 'uploads', 'gps_files', '142023-01_1695010659.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:32:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:32:30 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlsda.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:33:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:33:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:33:17 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlsda.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:33:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:33:17 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmls', 'kmlwwk.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:33:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:33:17 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('marker-map.png')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:33:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:33:17 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlwwkto.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:33:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:33:17 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlsda.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:35:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:07 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlsda.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:07 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmls', 'kmlwwk.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:07 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlwwkto.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:07 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('marker-map.png')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:07 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlsda.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:35:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:41 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('FCPATH\\public', 'uploads', 'gps_files', '142023-01_1695010659.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:41 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlwwkto.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:41 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('marker-map.png')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:41 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlsda.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:41 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmls', 'kmlwwk.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:50 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlsda.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:50 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmls', 'kmlwwk.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:50 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('kmlwwkto.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:35:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:35:50 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('FCPATH\\public', 'uploads', 'gps_files', '142023-01_1695010659.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:38:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:38:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:38:54 --> Trying to access array offset on value of type null
in APPPATH\Controllers\Projects.php on line 255.
 1 APPPATH\Controllers\Projects.php(255): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\Projects.php', 255)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\Projects->open_projects('FCPATH\\public', 'uploads', 'gps_files', '142023-01_1695010659.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Projects))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:39:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:40:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:41:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:42:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:43:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:43:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:44:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:44:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:44:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:44:53 --> Trying to access array offset on value of type null
in APPPATH\Controllers\POfficers.php on line 83.
 1 APPPATH\Controllers\POfficers.php(83): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\POfficers.php', 83)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\POfficers->po_details('FCPATH\\public', 'uploads', 'gps_files', '142023-01_1695010659.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\POfficers))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:47:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:47:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2023-09-18 04:47:45 --> Trying to access array offset on value of type null
in APPPATH\Controllers\POfficers.php on line 83.
 1 APPPATH\Controllers\POfficers.php(83): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'APPPATH\\Controllers\\POfficers.php', 83)
 2 SYSTEMPATH\CodeIgniter.php(934): App\Controllers\POfficers->po_details('FCPATH\\public', 'uploads', 'gps_files', '142023-01_1695010659.kml')
 3 SYSTEMPATH\CodeIgniter.php(499): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\POfficers))
 4 SYSTEMPATH\CodeIgniter.php(368): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
INFO - 2023-09-18 04:49:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:49:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:50:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:50:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:50:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:51:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:51:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:51:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:51:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:51:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:52:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:52:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2023-09-18 04:53:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
