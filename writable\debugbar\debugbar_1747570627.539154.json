{"url": "http://localhost/promis/index.php/new_projects", "method": "GET", "isAJAX": false, "startTime": **********.289886, "totalTime": 168.9, "totalMemory": "7.981", "segmentDuration": 25, "segmentCount": 7, "CI_VERSION": "4.3.2", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.322751, "duration": 0.*****************}, {"name": "Routing", "component": "Timer", "start": **********.370478, "duration": 0.00015497207641601562}, {"name": "Before Filters", "component": "Timer", "start": **********.37594, "duration": 0.****************}, {"name": "Controller", "component": "Timer", "start": **********.395448, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.395451, "duration": 0.038121938705444336}, {"name": "After Filters", "component": "Timer", "start": **********.458757, "duration": 0.0005800724029541016}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(3 total Queries, 3 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.83 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `selection`\n<strong>WHERE</strong> `box` = &#039;fund&#039;\n<strong>ORDER</strong> <strong>BY</strong> `item` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:203", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:557", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Projects.php:155", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Projects->create_projects()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Projects.php:155", "qid": "03ff6654bb3ef3e1dffd95fb3d6cf00d"}, {"hover": "", "class": "", "duration": "0.46 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `adx_country`\n<strong>WHERE</strong> `code` = &#039;pg&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:268", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:656", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Projects.php:156", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Projects->create_projects()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Projects.php:156", "qid": "77a339c1c0f67f23b8c3188352f4d760"}, {"hover": "", "class": "", "duration": "0.44 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `adx_province`\n<strong>WHERE</strong> `country_id` = &#039;1&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:203", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:557", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Projects.php:157", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Projects->create_projects()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Projects.php:157", "qid": "c86a4d2f8b7fac02a7488eee1f66e089"}]}, "badgeValue": 3, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.442881, "duration": "0.002996"}, {"name": "Query", "component": "Database", "start": **********.4478, "duration": "0.000829", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `selection`\n<strong>WHERE</strong> `box` = &#039;fund&#039;\n<strong>ORDER</strong> <strong>BY</strong> `item` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.451338, "duration": "0.000461", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `adx_country`\n<strong>WHERE</strong> `code` = &#039;pg&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.452, "duration": "0.000440", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `adx_province`\n<strong>WHERE</strong> `country_id` = &#039;1&#039;"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "info", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: templates/adminlte/admindash.php", "component": "Views", "start": **********.456633, "duration": 0.001428842544555664}, {"name": "View: projects/create_projects.php", "component": "Views", "start": **********.452628, "duration": 0.005921125411987305}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 181 )", "display": {"coreFiles": [{"name": "AutoRouterImproved.php", "path": "SYSTEMPATH\\Router\\AutoRouterImproved.php"}, {"name": "AutoRouterInterface.php", "path": "SYSTEMPATH\\Router\\AutoRouterInterface.php"}, {"name": "AutoloadConfig.php", "path": "SYSTEMPATH\\Config\\AutoloadConfig.php"}, {"name": "Autoloader.php", "path": "SYSTEMPATH\\Autoloader\\Autoloader.php"}, {"name": "BaseBuilder.php", "path": "SYSTEMPATH\\Database\\BaseBuilder.php"}, {"name": "BaseCollector.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php"}, {"name": "BaseConfig.php", "path": "SYSTEMPATH\\Config\\BaseConfig.php"}, {"name": "BaseConnection.php", "path": "SYSTEMPATH\\Database\\BaseConnection.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php"}, {"name": "BaseModel.php", "path": "SYSTEMPATH\\BaseModel.php"}, {"name": "BaseResult.php", "path": "SYSTEMPATH\\Database\\BaseResult.php"}, {"name": "BaseService.php", "path": "SYSTEMPATH\\Config\\BaseService.php"}, {"name": "Builder.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php"}, {"name": "CacheFactory.php", "path": "SYSTEMPATH\\Cache\\CacheFactory.php"}, {"name": "CacheInterface.php", "path": "SYSTEMPATH\\Cache\\CacheInterface.php"}, {"name": "CloneableCookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php"}, {"name": "CodeIgniter.php", "path": "SYSTEMPATH\\CodeIgniter.php"}, {"name": "Common.php", "path": "SYSTEMPATH\\Common.php"}, {"name": "ConditionalTrait.php", "path": "SYSTEMPATH\\Traits\\ConditionalTrait.php"}, {"name": "Config.php", "path": "SYSTEMPATH\\Database\\Config.php"}, {"name": "Connection.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php"}, {"name": "ConnectionInterface.php", "path": "SYSTEMPATH\\Database\\ConnectionInterface.php"}, {"name": "ContentSecurityPolicy.php", "path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php"}, {"name": "Controller.php", "path": "SYSTEMPATH\\Controller.php"}, {"name": "Cookie.php", "path": "SYSTEMPATH\\Cookie\\Cookie.php"}, {"name": "CookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CookieInterface.php"}, {"name": "CookieStore.php", "path": "SYSTEMPATH\\Cookie\\CookieStore.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Database\\Database.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php"}, {"name": "DebugToolbar.php", "path": "SYSTEMPATH\\Filters\\DebugToolbar.php"}, {"name": "DotEnv.php", "path": "SYSTEMPATH\\Config\\DotEnv.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Events\\Events.php"}, {"name": "Exceptions.php", "path": "SYSTEMPATH\\Debug\\Exceptions.php"}, {"name": "Factories.php", "path": "SYSTEMPATH\\Config\\Factories.php"}, {"name": "Factory.php", "path": "SYSTEMPATH\\Config\\Factory.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php"}, {"name": "FileLocator.php", "path": "SYSTEMPATH\\Autoloader\\FileLocator.php"}, {"name": "Files.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php"}, {"name": "FilterInterface.php", "path": "SYSTEMPATH\\Filters\\FilterInterface.php"}, {"name": "Filters.php", "path": "SYSTEMPATH\\Filters\\Filters.php"}, {"name": "FormatRules.php", "path": "SYSTEMPATH\\Validation\\FormatRules.php"}, {"name": "HandlerInterface.php", "path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php"}, {"name": "Header.php", "path": "SYSTEMPATH\\HTTP\\Header.php"}, {"name": "IncomingRequest.php", "path": "SYSTEMPATH\\HTTP\\IncomingRequest.php"}, {"name": "Logger.php", "path": "SYSTEMPATH\\Log\\Logger.php"}, {"name": "Logs.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php"}, {"name": "Message.php", "path": "SYSTEMPATH\\HTTP\\Message.php"}, {"name": "MessageInterface.php", "path": "SYSTEMPATH\\HTTP\\MessageInterface.php"}, {"name": "MessageTrait.php", "path": "SYSTEMPATH\\HTTP\\MessageTrait.php"}, {"name": "Model.php", "path": "SYSTEMPATH\\Model.php"}, {"name": "Modules.php", "path": "SYSTEMPATH\\Modules\\Modules.php"}, {"name": "OutgoingRequest.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php"}, {"name": "OutgoingRequestInterface.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php"}, {"name": "Query.php", "path": "SYSTEMPATH\\Database\\Query.php"}, {"name": "QueryInterface.php", "path": "SYSTEMPATH\\Database\\QueryInterface.php"}, {"name": "RendererInterface.php", "path": "SYSTEMPATH\\View\\RendererInterface.php"}, {"name": "Request.php", "path": "SYSTEMPATH\\HTTP\\Request.php"}, {"name": "RequestInterface.php", "path": "SYSTEMPATH\\HTTP\\RequestInterface.php"}, {"name": "RequestTrait.php", "path": "SYSTEMPATH\\HTTP\\RequestTrait.php"}, {"name": "Response.php", "path": "SYSTEMPATH\\HTTP\\Response.php"}, {"name": "ResponseInterface.php", "path": "SYSTEMPATH\\HTTP\\ResponseInterface.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\API\\ResponseTrait.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\HTTP\\ResponseTrait.php"}, {"name": "Result.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Result.php"}, {"name": "ResultInterface.php", "path": "SYSTEMPATH\\Database\\ResultInterface.php"}, {"name": "RouteCollection.php", "path": "SYSTEMPATH\\Router\\RouteCollection.php"}, {"name": "RouteCollectionInterface.php", "path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php"}, {"name": "Router.php", "path": "SYSTEMPATH\\Router\\Router.php"}, {"name": "RouterInterface.php", "path": "SYSTEMPATH\\Router\\RouterInterface.php"}, {"name": "Routes.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php"}, {"name": "Services.php", "path": "SYSTEMPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "SYSTEMPATH\\Session\\Session.php"}, {"name": "SessionInterface.php", "path": "SYSTEMPATH\\Session\\SessionInterface.php"}, {"name": "Time.php", "path": "SYSTEMPATH\\I18n\\Time.php"}, {"name": "TimeTrait.php", "path": "SYSTEMPATH\\I18n\\TimeTrait.php"}, {"name": "Timer.php", "path": "SYSTEMPATH\\Debug\\Timer.php"}, {"name": "Timers.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php"}, {"name": "Toolbar.php", "path": "SYSTEMPATH\\Debug\\Toolbar.php"}, {"name": "URI.php", "path": "SYSTEMPATH\\HTTP\\URI.php"}, {"name": "UserAgent.php", "path": "SYSTEMPATH\\HTTP\\UserAgent.php"}, {"name": "Validation.php", "path": "SYSTEMPATH\\Validation\\Validation.php"}, {"name": "ValidationInterface.php", "path": "SYSTEMPATH\\Validation\\ValidationInterface.php"}, {"name": "View.php", "path": "SYSTEMPATH\\Config\\View.php"}, {"name": "View.php", "path": "SYSTEMPATH\\View\\View.php"}, {"name": "ViewDecoratorTrait.php", "path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php"}, {"name": "Views.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php"}, {"name": "array_helper.php", "path": "SYSTEMPATH\\Helpers\\array_helper.php"}, {"name": "bootstrap.php", "path": "SYSTEMPATH\\bootstrap.php"}, {"name": "form_helper.php", "path": "SYSTEMPATH\\Helpers\\form_helper.php"}, {"name": "kint_helper.php", "path": "SYSTEMPATH\\Helpers\\kint_helper.php"}, {"name": "url_helper.php", "path": "SYSTEMPATH\\Helpers\\url_helper.php"}], "userFiles": [{"name": "AbstractRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\AbstractRenderer.php"}, {"name": "App.php", "path": "APPPATH\\Config\\App.php"}, {"name": "Auth.php", "path": "APPPATH\\Filters\\Auth.php"}, {"name": "Autoload.php", "path": "APPPATH\\Config\\Autoload.php"}, {"name": "BaseController.php", "path": "APPPATH\\Controllers\\BaseController.php"}, {"name": "Cache.php", "path": "APPPATH\\Config\\Cache.php"}, {"name": "ClassLoader.php", "path": "FCPATH\\vendor\\composer\\ClassLoader.php"}, {"name": "CliRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\CliRenderer.php"}, {"name": "Common.php", "path": "APPPATH\\Common.php"}, {"name": "Constants.php", "path": "APPPATH\\Config\\Constants.php"}, {"name": "ContentSecurityPolicy.php", "path": "APPPATH\\Config\\ContentSecurityPolicy.php"}, {"name": "Cookie.php", "path": "APPPATH\\Config\\Cookie.php"}, {"name": "Database.php", "path": "APPPATH\\Config\\Database.php"}, {"name": "DocTypes.php", "path": "APPPATH\\Config\\DocTypes.php"}, {"name": "Escaper.php", "path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\Escaper.php"}, {"name": "Events.php", "path": "APPPATH\\Config\\Events.php"}, {"name": "Exceptions.php", "path": "APPPATH\\Config\\Exceptions.php"}, {"name": "FacadeInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\FacadeInterface.php"}, {"name": "Feature.php", "path": "APPPATH\\Config\\Feature.php"}, {"name": "Filters.php", "path": "APPPATH\\Config\\Filters.php"}, {"name": "Functions.php", "path": "FCPATH\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php"}, {"name": "InstalledVersions.php", "path": "FCPATH\\vendor\\composer\\InstalledVersions.php"}, {"name": "Kint.php", "path": "APPPATH\\Config\\Kint.php"}, {"name": "Kint.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Kint.php"}, {"name": "LogLevel.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LogLevel.php"}, {"name": "Logger.php", "path": "APPPATH\\Config\\Logger.php"}, {"name": "LoggerAwareTrait.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerAwareTrait.php"}, {"name": "LoggerInterface.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerInterface.php"}, {"name": "Modules.php", "path": "APPPATH\\Config\\Modules.php"}, {"name": "Paths.php", "path": "APPPATH\\Config\\Paths.php"}, {"name": "Projects.php", "path": "APPPATH\\Controllers\\Projects.php"}, {"name": "RendererInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RendererInterface.php"}, {"name": "RichRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RichRenderer.php"}, {"name": "Routes.php", "path": "APPPATH\\Config\\Routes.php"}, {"name": "Services.php", "path": "APPPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "APPPATH\\Config\\Session.php"}, {"name": "TextRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\TextRenderer.php"}, {"name": "Toolbar.php", "path": "APPPATH\\Config\\Toolbar.php"}, {"name": "UserAgents.php", "path": "APPPATH\\Config\\UserAgents.php"}, {"name": "Utils.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Utils.php"}, {"name": "Validation.php", "path": "APPPATH\\Config\\Validation.php"}, {"name": "View.php", "path": "APPPATH\\Config\\View.php"}, {"name": "admindash.php", "path": "APPPATH\\Views\\templates\\adminlte\\admindash.php"}, {"name": "autoload.php", "path": "FCPATH\\vendor\\autoload.php"}, {"name": "autoload_real.php", "path": "FCPATH\\vendor\\composer\\autoload_real.php"}, {"name": "autoload_static.php", "path": "FCPATH\\vendor\\composer\\autoload_static.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php80\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php81\\bootstrap.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php"}, {"name": "contractorsModel.php", "path": "APPPATH\\Models\\contractorsModel.php"}, {"name": "contractorsNoticesModel.php", "path": "APPPATH\\Models\\contractorsNoticesModel.php"}, {"name": "countryModel.php", "path": "APPPATH\\Models\\countryModel.php"}, {"name": "create_projects.php", "path": "APPPATH\\Views\\projects\\create_projects.php"}, {"name": "deep_copy.php", "path": "FCPATH\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php"}, {"name": "development.php", "path": "APPPATH\\Config\\Boot\\development.php"}, {"name": "districtModel.php", "path": "APPPATH\\Models\\districtModel.php"}, {"name": "eventfilesModel.php", "path": "APPPATH\\Models\\eventfilesModel.php"}, {"name": "function.php", "path": "FCPATH\\vendor\\symfony\\deprecation-contracts\\function.php"}, {"name": "functions.php", "path": "FCPATH\\vendor\\symfony\\string\\Resources\\functions.php"}, {"name": "index.php", "path": "FCPATH\\index.php"}, {"name": "info_helper.php", "path": "APPPATH\\Helpers\\info_helper.php"}, {"name": "init.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init.php"}, {"name": "init_helpers.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init_helpers.php"}, {"name": "installed.php", "path": "FCPATH\\vendor\\composer\\installed.php"}, {"name": "kmlfilesModel.php", "path": "APPPATH\\Models\\kmlfilesModel.php"}, {"name": "llgModel.php", "path": "APPPATH\\Models\\llgModel.php"}, {"name": "platform_check.php", "path": "FCPATH\\vendor\\composer\\platform_check.php"}, {"name": "prodocsModel.php", "path": "APPPATH\\Models\\prodocsModel.php"}, {"name": "proeventsModel.php", "path": "APPPATH\\Models\\proeventsModel.php"}, {"name": "profundModel.php", "path": "APPPATH\\Models\\profundModel.php"}, {"name": "project_officersModel.php", "path": "APPPATH\\Models\\project_officersModel.php"}, {"name": "projectsModel.php", "path": "APPPATH\\Models\\projectsModel.php"}, {"name": "promilefilesModel.php", "path": "APPPATH\\Models\\promilefilesModel.php"}, {"name": "promilestonesModel.php", "path": "APPPATH\\Models\\promilestonesModel.php"}, {"name": "prophasesModel.php", "path": "APPPATH\\Models\\prophasesModel.php"}, {"name": "provinceModel.php", "path": "APPPATH\\Models\\provinceModel.php"}, {"name": "selectionModel.php", "path": "APPPATH\\Models\\selectionModel.php"}, {"name": "settingsModel.php", "path": "APPPATH\\Models\\settingsModel.php"}, {"name": "usersModel.php", "path": "APPPATH\\Models\\usersModel.php"}]}, "badgeValue": 181, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Projects", "method": "create_projects", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\Home::logout"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Home::about"}, {"method": "GET", "route": "home_project_one_view/(.*)", "handler": "\\App\\Controllers\\Home::home_project_one_view/$1"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\Admindash::index"}, {"method": "GET", "route": "my_account", "handler": "\\App\\Controllers\\Admindash::my_account"}, {"method": "GET", "route": "reports_dashboard", "handler": "\\App\\Controllers\\Admindash::reports_dashboard"}, {"method": "GET", "route": "report_projects_dash", "handler": "\\App\\Controllers\\ProReports::index"}, {"method": "GET", "route": "report_projects_status/(.*)", "handler": "\\App\\Controllers\\ProReports::report_projects_status/$1"}, {"method": "GET", "route": "report_projects_view/(.*)", "handler": "\\App\\Controllers\\ProReports::report_projects_view/$1"}, {"method": "GET", "route": "report_pro_payment_record/(.*)", "handler": "\\App\\Controllers\\ProReports::report_pro_payment_record/$1"}, {"method": "GET", "route": "report_contractors_dash", "handler": "\\App\\Controllers\\ProReports::report_contractors_dash"}, {"method": "GET", "route": "report_contractors_view/(.*)", "handler": "\\App\\Controllers\\ProReports::report_contractors_view/$1"}, {"method": "GET", "route": "report_pro_officers_dash", "handler": "\\App\\Controllers\\ProReports::report_pro_officers_dash"}, {"method": "GET", "route": "report_pro_officers_view/(.*)", "handler": "\\App\\Controllers\\ProReports::report_pro_officers_view/$1"}, {"method": "GET", "route": "po_dash", "handler": "\\App\\Controllers\\POfficers::index"}, {"method": "GET", "route": "po_open_project/(.*)", "handler": "\\App\\Controllers\\POfficers::po_open_project/$1"}, {"method": "GET", "route": "po_details/(.*)", "handler": "\\App\\Controllers\\POfficers::po_details/$1"}, {"method": "GET", "route": "po_details_info_edit/(.*)", "handler": "\\App\\Controllers\\POfficers::po_details_info_edit/$1"}, {"method": "GET", "route": "po_phases/(.*)", "handler": "\\App\\Controllers\\POfficers::po_phases/$1"}, {"method": "GET", "route": "po_milestones/(.*)", "handler": "\\App\\Controllers\\POfficers::po_milestones/$1"}, {"method": "GET", "route": "po_files_open/(.*)", "handler": "\\App\\Controllers\\POfficers::po_files_open/$1"}, {"method": "GET", "route": "po_funding_open/(.*)", "handler": "\\App\\Controllers\\POfficers::po_funding_open/$1"}, {"method": "GET", "route": "po_events_open/(.*)", "handler": "\\App\\Controllers\\POfficers::po_events_open/$1"}, {"method": "GET", "route": "po_reports_open/(.*)", "handler": "\\App\\Controllers\\POfficers::po_reports_open/$1"}, {"method": "GET", "route": "projects", "handler": "\\App\\Controllers\\Projects::index"}, {"method": "GET", "route": "new_projects", "handler": "\\App\\Controllers\\Projects::create_projects"}, {"method": "GET", "route": "open_projects/(.*)", "handler": "\\App\\Controllers\\Projects::open_projects/$1"}, {"method": "GET", "route": "open_prophases/(.*)", "handler": "\\App\\Controllers\\Projects::open_prophases/$1"}, {"method": "GET", "route": "project_phases/(.*)", "handler": "\\App\\Controllers\\Projects::project_phases/$1"}, {"method": "GET", "route": "edit_projects/(.*)", "handler": "\\App\\Controllers\\Projects::edit_projects/$1"}, {"method": "GET", "route": "milestones/(.*)", "handler": "\\App\\Controllers\\Milestones::pro_milestones/$1"}, {"method": "GET", "route": "open_proevents/(.*)", "handler": "\\App\\Controllers\\Projects::open_proevents/$1"}, {"method": "GET", "route": "getaddress", "handler": "\\App\\Controllers\\Projects::getaddress"}, {"method": "GET", "route": "edit_projects_status/(.*)", "handler": "\\App\\Controllers\\Projects::edit_projects_status/$1"}, {"method": "GET", "route": "edit_projects_contractors/(.*)", "handler": "\\App\\Controllers\\Projects::edit_projects_contractors/$1"}, {"method": "GET", "route": "edit_projects_officers/(.*)", "handler": "\\App\\Controllers\\Projects::edit_projects_officers/$1"}, {"method": "GET", "route": "project_officers", "handler": "\\App\\Controllers\\Project_officers::index"}, {"method": "GET", "route": "proleads", "handler": "\\App\\Controllers\\Proleads::index"}, {"method": "GET", "route": "contractors", "handler": "\\App\\Controllers\\Proleads::contractors_list"}, {"method": "GET", "route": "contractors_new", "handler": "\\App\\Controllers\\Proleads::contractors_new"}, {"method": "GET", "route": "edit_contractors/(.*)", "handler": "\\App\\Controllers\\Proleads::edit_contractors/$1"}, {"method": "GET", "route": "open_contractor/(.*)", "handler": "\\App\\Controllers\\Proleads::open_contractor/$1"}, {"method": "GET", "route": "da<PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::index"}, {"method": "GET", "route": "dlogout", "handler": "\\App\\Controllers\\Dakoii::logout"}, {"method": "GET", "route": "ddash", "handler": "\\App\\Controllers\\Dakoii::ddash"}, {"method": "GET", "route": "dopen_org/(.*)", "handler": "\\App\\Controllers\\Dakoii::open_org/$1"}, {"method": "GET", "route": "dlist_org", "handler": "\\App\\Controllers\\Dakoii::list_org"}, {"method": "GET", "route": "testa", "handler": "\\App\\Controllers\\Test::index"}, {"method": "GET", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "GET", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}, {"method": "GET", "route": "testmap", "handler": "\\App\\Controllers\\Test::testmap"}, {"method": "GET", "route": "test_view", "handler": "\\App\\Controllers\\Test::test_view"}, {"method": "POST", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "POST", "route": "login_po", "handler": "\\App\\Controllers\\Home::login_po"}, {"method": "POST", "route": "update_admin_orglogo", "handler": "\\App\\Controllers\\Admindash::update_admin_orglogo"}, {"method": "POST", "route": "update_admin_orginfo", "handler": "\\App\\Controllers\\Admindash::update_admin_orginfo"}, {"method": "POST", "route": "milestone_notes", "handler": "\\App\\Controllers\\POfficers::milestone_notes"}, {"method": "POST", "route": "milestone_files", "handler": "\\App\\Controllers\\POfficers::milestone_files"}, {"method": "POST", "route": "getdistricts", "handler": "\\App\\Controllers\\POfficers::getdistricts"}, {"method": "POST", "route": "edit_project_budget", "handler": "\\App\\Controllers\\Projects::edit_project_budget"}, {"method": "POST", "route": "set_project_contractor", "handler": "\\App\\Controllers\\Projects::set_project_contractor"}, {"method": "POST", "route": "set_project_officers", "handler": "\\App\\Controllers\\Projects::set_project_officers"}, {"method": "POST", "route": "add_proevents", "handler": "\\App\\Controllers\\Projects::add_proevents"}, {"method": "POST", "route": "edit_proevents", "handler": "\\App\\Controllers\\Projects::edit_proevents"}, {"method": "POST", "route": "edit_projects/(.*)", "handler": "\\App\\Controllers\\Projects::edit_projects/$1"}, {"method": "POST", "route": "new_projects", "handler": "\\App\\Controllers\\Projects::create_projects"}, {"method": "POST", "route": "update_projects_status", "handler": "\\App\\Controllers\\Projects::update_projects_status"}, {"method": "POST", "route": "update_projects_contractors", "handler": "\\App\\Controllers\\Projects::update_projects_contractors"}, {"method": "POST", "route": "update_projects_officers", "handler": "\\App\\Controllers\\Projects::update_projects_officers"}, {"method": "POST", "route": "getaddress", "handler": "\\App\\Controllers\\Projects::getaddress"}, {"method": "POST", "route": "gps_upload", "handler": "\\App\\Controllers\\Projects::gpsfile_upload"}, {"method": "POST", "route": "prodocs_upload", "handler": "\\App\\Controllers\\Projects::prodocs_upload"}, {"method": "POST", "route": "prodocs_edit", "handler": "\\App\\Controllers\\Projects::prodocs_edit"}, {"method": "POST", "route": "prodocs_delete", "handler": "\\App\\Controllers\\Projects::prodocs_delete"}, {"method": "POST", "route": "gps_set", "handler": "\\App\\Controllers\\Projects::gps_set"}, {"method": "POST", "route": "addpayments", "handler": "\\App\\Controllers\\Projects::addpayments"}, {"method": "POST", "route": "editpayments", "handler": "\\App\\Controllers\\Projects::editpayments"}, {"method": "POST", "route": "milestones/(.*)", "handler": "\\App\\Controllers\\Projects::pro_milestones/$1"}, {"method": "POST", "route": "add_phases", "handler": "\\App\\Controllers\\Projects::add_phases"}, {"method": "POST", "route": "edit_phases", "handler": "\\App\\Controllers\\Projects::edit_phases"}, {"method": "POST", "route": "delete_phases", "handler": "\\App\\Controllers\\Projects::delete_phases"}, {"method": "POST", "route": "add_milestones", "handler": "\\App\\Controllers\\Projects::add_milestones"}, {"method": "POST", "route": "edit_milestones", "handler": "\\App\\Controllers\\Projects::edit_milestones"}, {"method": "POST", "route": "delete_milestones", "handler": "\\App\\Controllers\\Projects::delete_milestones"}, {"method": "POST", "route": "pro_status", "handler": "\\App\\Controllers\\Projects::pro_status"}, {"method": "POST", "route": "add_project_officers", "handler": "\\App\\Controllers\\Project_officers::add_project_officers"}, {"method": "POST", "route": "edit_project_officers", "handler": "\\App\\Controllers\\Project_officers::edit_project_officers"}, {"method": "POST", "route": "edit_password_project_officers", "handler": "\\App\\Controllers\\Project_officers::edit_password_project_officers"}, {"method": "POST", "route": "create_contractor", "handler": "\\App\\Controllers\\Proleads::create_contractor"}, {"method": "POST", "route": "update_contractor", "handler": "\\App\\Controllers\\Proleads::update_contractor"}, {"method": "POST", "route": "update_con_contacts", "handler": "\\App\\Controllers\\Proleads::update_con_contacts"}, {"method": "POST", "route": "create_con_files", "handler": "\\App\\Controllers\\Proleads::create_con_files"}, {"method": "POST", "route": "update_con_files", "handler": "\\App\\Controllers\\Proleads::update_con_files"}, {"method": "POST", "route": "delete_con_files", "handler": "\\App\\Controllers\\Proleads::delete_con_files"}, {"method": "POST", "route": "update_con_logo", "handler": "\\App\\Controllers\\Proleads::update_con_logo"}, {"method": "POST", "route": "create_con_notices", "handler": "\\App\\Controllers\\Proleads::create_con_notices"}, {"method": "POST", "route": "dlogin", "handler": "\\App\\Controllers\\Dakoii::login"}, {"method": "POST", "route": "dad<PERSON>g", "handler": "\\App\\Controllers\\Dakoii::addorg"}, {"method": "POST", "route": "deditorg", "handler": "\\App\\Controllers\\Dakoii::editorg"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::adduser"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::create_admin"}, {"method": "POST", "route": "dakoii_update_org_logo", "handler": "\\App\\Controllers\\Dakoii::dakoii_update_org_logo"}, {"method": "POST", "route": "dakoii_update_org_address", "handler": "\\App\\Controllers\\Dakoii::dakoii_update_org_address"}, {"method": "POST", "route": "dakoii_update_org_location_lock", "handler": "\\App\\Controllers\\Dakoii::dakoii_update_org_location_lock"}, {"method": "POST", "route": "dakoii_remove_org_location_lock", "handler": "\\App\\Controllers\\Dakoii::dakoii_remove_org_location_lock"}, {"method": "POST", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "POST", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}]}, "badgeValue": 54, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "7.93", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.14", "count": 3}}}, "badgeValue": 4, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.326272, "duration": 0.00792694091796875}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.448642, "duration": 6.29425048828125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.451807, "duration": 4.38690185546875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.452445, "duration": 3.0040740966796875e-05}]}], "vars": {"varData": {"View Data": {"title": "New Projects", "menu": "addprojects", "select": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (2)</li><li>Contents (2)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>box</th><th>value</th><th>item</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">5</td><td title=\"string (4)\">fund</td><td title=\"string (2)\">eu</td><td title=\"string (2)\">EU</td></tr><tr><th>1</th><td title=\"string (1)\">4</td><td title=\"string (4)\">fund</td><td title=\"string (3)\">pip</td><td title=\"string (3)\">PIP</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>box</dfn> =&gt; <var>string</var> (4) \"fund\"<div class=\"access-path\">$value[0]['box']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>value</dfn> =&gt; <var>string</var> (2) \"eu\"<div class=\"access-path\">$value[0]['value']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>item</dfn> =&gt; <var>string</var> (2) \"EU\"<div class=\"access-path\">$value[0]['item']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>box</dfn> =&gt; <var>string</var> (4) \"fund\"<div class=\"access-path\">$value[1]['box']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>value</dfn> =&gt; <var>string</var> (3) \"pip\"<div class=\"access-path\">$value[1]['value']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>item</dfn> =&gt; <var>string</var> (3) \"PIP\"<div class=\"access-path\">$value[1]['item']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "set_country": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (16) \"Papua New Guinea\"<div class=\"access-path\">$value['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (2) \"PG\"<div class=\"access-path\">$value['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2023-03-11 10:10:42\"<div class=\"access-path\">$value['created_at']</div></dt></dl></dd></dl></div>", "get_provinces": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (21)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (21)</li><li>Contents (21)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>provincecode</th><th>name</th><th>country_id</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">86</td><td title=\"string (2)\">03</td><td title=\"string (16)\">Central Province</td><td title=\"string (1)\">1</td></tr><tr><th>1</th><td title=\"string (2)\">87</td><td title=\"string (2)\">10</td><td title=\"string (15)\">Chimbu Province</td><td title=\"string (1)\">1</td></tr><tr><th>2</th><td title=\"string (2)\">88</td><td title=\"string (2)\">11</td><td title=\"string (26)\">Eastern Highlands Province</td><td title=\"string (1)\">1</td></tr><tr><th>3</th><td title=\"string (2)\">89</td><td title=\"string (2)\">18</td><td title=\"string (25)\">East New Britain Province</td><td title=\"string (1)\">1</td></tr><tr><th>4</th><td title=\"string (2)\">90</td><td title=\"string (4)\">PG14</td><td title=\"string (19)\">East Sepik Province</td><td title=\"string (1)\">1</td></tr><tr><th>5</th><td title=\"string (2)\">91</td><td title=\"string (2)\">08</td><td title=\"string (13)\">Enga Province</td><td title=\"string (1)\">1</td></tr><tr><th>6</th><td title=\"string (2)\">92</td><td title=\"string (2)\">02</td><td title=\"string (13)\">Gulf Province</td><td title=\"string (1)\">1</td></tr><tr><th>7</th><td title=\"string (2)\">93</td><td title=\"string (2)\">21</td><td title=\"string (13)\">Hela Province</td><td title=\"string (1)\">1</td></tr><tr><th>8</th><td title=\"string (2)\">94</td><td title=\"string (2)\">22</td><td title=\"string (15)\">Jiwaka Province</td><td title=\"string (1)\">1</td></tr><tr><th>9</th><td title=\"string (2)\">95</td><td title=\"string (2)\">13</td><td title=\"string (15)\">Madang Province</td><td title=\"string (1)\">1</td></tr><tr><th>10</th><td title=\"string (2)\">96</td><td title=\"string (2)\">16</td><td title=\"string (14)\">Manus Province</td><td title=\"string (1)\">1</td></tr><tr><th>11</th><td title=\"string (2)\">97</td><td title=\"string (2)\">05</td><td title=\"string (18)\">Milne Bay Province</td><td title=\"string (1)\">1</td></tr><tr><th>12</th><td title=\"string (2)\">98</td><td title=\"string (2)\">12</td><td title=\"string (15)\">Morobe Province</td><td title=\"string (1)\">1</td></tr><tr><th>13</th><td title=\"string (2)\">99</td><td title=\"string (2)\">17</td><td title=\"string (20)\">New Ireland Province</td><td title=\"string (1)\">1</td></tr><tr><th>14</th><td title=\"string (3)\">100</td><td title=\"string (2)\">06</td><td title=\"string (12)\">Oro Province</td><td title=\"string (1)\">1</td></tr><tr><th>15</th><td title=\"string (3)\">101</td><td title=\"string (2)\">07</td><td title=\"string (27)\">Southern Highlands Province</td><td title=\"string (1)\">1</td></tr><tr><th>16</th><td title=\"string (3)\">102</td><td title=\"string (2)\">01</td><td title=\"string (16)\">Western Province</td><td title=\"string (1)\">1</td></tr><tr><th>17</th><td title=\"string (3)\">103</td><td title=\"string (2)\">09</td><td title=\"string (26)\">Western Highlands Province</td><td title=\"string (1)\">1</td></tr><tr><th>18</th><td title=\"string (3)\">104</td><td title=\"string (2)\">19</td><td title=\"string (25)\">West New Britain Province</td><td title=\"string (1)\">1</td></tr><tr><th>19</th><td title=\"string (3)\">105</td><td title=\"string (2)\">20</td><td title=\"string (17)\">AROB Bougainville</td><td title=\"string (1)\">1</td></tr><tr><th>20</th><td title=\"string (3)\">106</td><td title=\"string (4)\">PG15</td><td title=\"string (19)\">West Sepik Province</td><td title=\"string (1)\">1</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"86\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"03\"<div class=\"access-path\">$value[0]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (16) \"Central Province\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"87\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[1]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Chimbu Province\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"88\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[2]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (26) \"Eastern Highlands Province\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"89\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[3]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (25) \"East New Britain Province\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"90\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (4) \"PG14\"<div class=\"access-path\">$value[4]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (19) \"East Sepik Province\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"91\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"08\"<div class=\"access-path\">$value[5]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (13) \"Enga Province\"<div class=\"access-path\">$value[5]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"92\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"02\"<div class=\"access-path\">$value[6]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (13) \"Gulf Province\"<div class=\"access-path\">$value[6]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"93\"<div class=\"access-path\">$value[7]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[7]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (13) \"Hela Province\"<div class=\"access-path\">$value[7]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[7]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"94\"<div class=\"access-path\">$value[8]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[8]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Jiwaka Province\"<div class=\"access-path\">$value[8]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[8]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"95\"<div class=\"access-path\">$value[9]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value[9]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Madang Province\"<div class=\"access-path\">$value[9]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[9]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"96\"<div class=\"access-path\">$value[10]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[10]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Manus Province\"<div class=\"access-path\">$value[10]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[10]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"97\"<div class=\"access-path\">$value[11]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"05\"<div class=\"access-path\">$value[11]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (18) \"Milne Bay Province\"<div class=\"access-path\">$value[11]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[11]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>12</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[12]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"98\"<div class=\"access-path\">$value[12]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[12]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Morobe Province\"<div class=\"access-path\">$value[12]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[12]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>13</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[13]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"99\"<div class=\"access-path\">$value[13]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[13]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (20) \"New Ireland Province\"<div class=\"access-path\">$value[13]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[13]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>14</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[14]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (3) \"100\"<div class=\"access-path\">$value[14]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"06\"<div class=\"access-path\">$value[14]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (12) \"Oro Province\"<div class=\"access-path\">$value[14]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[14]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>15</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[15]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (3) \"101\"<div class=\"access-path\">$value[15]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"07\"<div class=\"access-path\">$value[15]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (27) \"Southern Highlands Province\"<div class=\"access-path\">$value[15]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[15]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>16</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[16]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (3) \"102\"<div class=\"access-path\">$value[16]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"01\"<div class=\"access-path\">$value[16]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (16) \"Western Province\"<div class=\"access-path\">$value[16]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[16]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>17</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[17]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (3) \"103\"<div class=\"access-path\">$value[17]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"09\"<div class=\"access-path\">$value[17]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (26) \"Western Highlands Province\"<div class=\"access-path\">$value[17]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[17]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>18</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[18]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (3) \"104\"<div class=\"access-path\">$value[18]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[18]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (25) \"West New Britain Province\"<div class=\"access-path\">$value[18]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[18]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>19</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[19]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (3) \"105\"<div class=\"access-path\">$value[19]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (2) \"20\"<div class=\"access-path\">$value[19]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (17) \"AROB Bougainville\"<div class=\"access-path\">$value[19]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[19]['country_id']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>20</dfn> =&gt; <var>array</var> (4)<div class=\"access-path\">$value[20]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (3) \"106\"<div class=\"access-path\">$value[20]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>provincecode</dfn> =&gt; <var>string</var> (4) \"PG15\"<div class=\"access-path\">$value[20]['provincecode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>name</dfn> =&gt; <var>string</var> (19) \"West Sepik Province\"<div class=\"access-path\">$value[20]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>country_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[20]['country_id']</div></dt></dl></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1747570499</pre>", "_ci_previous_url": "http://localhost/promis/index.php/", "username": "minad", "name": "<PERSON><PERSON>", "role": "admin", "status": "1", "orgname": "East Sepik Provincial Administration", "orglogo": "public/uploads/org_logo/2345_1707199489.png", "orgcode": "2345", "org_lock_code": "PG14", "org_lock_name": "East Sepik Province", "org_lock_level": "province", "org_cgps_lon": "143.293196", "org_cgps_lat": "-4.294692", "org_cgps_zoom": "8", "is_logged_in": "yes"}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost/promis/dashboard", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9", "Cookie": "ci_session=49gsblks3oh2t1s0l9rgg45u2s1muehn"}, "cookies": {"ci_session": "49gsblks3oh2t1s0l9rgg45u2s1muehn"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.3.2", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/promis", "timezone": "Pacific/Port_Moresby", "locale": "en", "cspEnabled": false}}