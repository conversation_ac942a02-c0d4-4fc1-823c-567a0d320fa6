<?= $this->extend("templates/nolsadmintemp"); ?>
<?= $this->section('content'); ?>

<body>
    <div class="container-fluid">
        <div class="row p-1">
            <div class="col-12 d-flex justify-content-between">

                <h4><?= $pro['procode'] . "-" . $pro['name'] ?></h4>

                <nav class="breadcrumb">
                    <a class="breadcrumb-item" href="<?= base_url() ?>/po_open_project/<?= $pro['ucode'] ?>"> <i class="bi bi-chevron-left"></i> Go Back</a>
                    <span class="breadcrumb-item active">Files</span>
                </nav>

            </div>

        </div>

        <div class="row pb-2">
            <div class="col-md-12">
                <h5 class="text-center">Project Documents</h5>
            </div>
        </div>

        <div class="row">

            <div class="col-md-12">

                <!-- Button trigger modal -->
                <button type="button" class="btn btn-primary justify-content-end float-right m-1" data-toggle="modal" data-target="#addprodocs" hover>
                    <i class=" fa fa-upload" aria-hidden="true"></i> Upload Project Files
                </button>

                <!-- Modal upload files -->
                <div class="modal fade" id="addprodocs" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"> <i class="fa fa-upload" aria-hidden="true"></i> Upload Project Files</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <?= form_open_multipart('prodocs_upload') ?>
                            <div class="modal-body">
                                <div class="form-group">
                                    <label for="exampleInputFile">File Title</label>
                                    <div class="input-group">
                                        <input type="text" name="name" placeholder="File Title" class=" form-control" required>
                                    </div>
                                    <label for="exampleInputFile">Upload Project Files</label>
                                    <div class="input-group">
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" name="prodocs" id="exampleInputFile" required>
                                            <label class="custom-file-label" for="exampleInputFile">Choose File
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">

                                <input type="hidden" name="procode" value="<?= $pro['procode'] ?>">
                                <input type="hidden" name="proid" value="<?= $pro['id'] ?>">

                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                <button type="submit" class="btn btn-primary"> <i class="fa fa-upload" aria-hidden="true"></i> Upload</button>
                            </div>
                            <?= form_close(); ?>
                        </div>
                    </div>
                </div>
                <!-- ./modal -->




            </div>

        </div>


        <div class="row mb-2 ">
            <div class="col-md-12">
                <h2>Files</h2>
            </div>
            <?php foreach ($prodocs as $file) : ?>
                <div class="col-md-2 p-2">
                    <div class="card  p-0">
                        <div class="card-header">
                            <?= $file['name'] ?>
                        </div>
                        <img class=" card-img-top" height="200" src="<?= imgfilecheck($file['filepath']) ?>" alt="">

                        <div class="card-footer d-flex justify-content-between">
                            <a href="<?= base_url($file['filepath']) ?>" class=""> <i class="fa fa-download" aria-hidden="true"></i>
                            (.<?= getfileExtension($file['filepath']) ?>)
                            </a>

                            <!-- Button trigger modal -->
                            <a href="#" class=" text-danger" data-toggle="modal" data-target="#del<?= $file['id'] ?> "> <i class="fas fa-trash-alt" aria-hidden="true"></i></a>

                            <!-- Modal -->
                            <div class="modal fade" id="del<?= $file['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger">
                                            <h5 class="modal-title text-light"> <i class="fa fa-exclamation-triangle" aria-hidden="true"></i> Your aboout this delete</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>

                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <img class="card-img-top" src="<?= imgfilecheck($file['filepath']) ?>" alt="">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer border-3 border-danger">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                            <button type="submit" class="btn btn-danger"> <i class="fa fa-times-circle" aria-hidden="true"></i> Delete</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- ./modal -->

                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>












    </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.slim.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
</body>



</html>
<?= $this->endSection() ?>